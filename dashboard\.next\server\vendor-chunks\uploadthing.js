"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/uploadthing";
exports.ids = ["vendor-chunks/uploadthing"];
exports.modules = {

/***/ "(ssr)/./node_modules/uploadthing/client/index.js":
/*!**************************************************!*\
  !*** ./node_modules/uploadthing/client/index.js ***!
  \**************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadAbortedError: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadAbortedError),\n/* harmony export */   UploadPausedError: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadPausedError),\n/* harmony export */   allowedContentTextLabelGenerator: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.allowedContentTextLabelGenerator),\n/* harmony export */   bytesToFileSize: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.bytesToFileSize),\n/* harmony export */   genUploader: () => (/* binding */ genUploader),\n/* harmony export */   generateClientDropzoneAccept: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateClientDropzoneAccept),\n/* harmony export */   generateMimeTypes: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generateMimeTypes),\n/* harmony export */   generatePermittedFileTypes: () => (/* reexport safe */ _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.generatePermittedFileTypes),\n/* harmony export */   isValidFileSize: () => (/* binding */ isValidFileSize),\n/* harmony export */   isValidFileType: () => (/* binding */ isValidFileType),\n/* harmony export */   version: () => (/* binding */ version)\n/* harmony export */ });\n/* harmony import */ var effect_Array__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! effect/Array */ \"(ssr)/./node_modules/effect/dist/esm/Array.js\");\n/* harmony import */ var effect_Micro__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! effect/Micro */ \"(ssr)/./node_modules/effect/dist/esm/Micro.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @uploadthing/shared */ \"(ssr)/./node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var _dist_internal_deferred_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ../dist/_internal/deferred.js */ \"(ssr)/./node_modules/uploadthing/dist/_internal/deferred.js\");\n/* harmony import */ var _dist_internal_upload_browser_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! ../dist/_internal/upload-browser.js */ \"(ssr)/./node_modules/uploadthing/dist/_internal/upload-browser.js\");\n/* harmony import */ var _dist_internal_ut_reporter_js__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ../dist/_internal/ut-reporter.js */ \"(ssr)/./node_modules/uploadthing/dist/_internal/ut-reporter.js\");\n\n\n\n\n\n\n\n\nvar version$1 = \"7.7.2\";\n\nconst version = version$1;\n/**\n * Validate that a file is of a valid type given a route config\n * @public\n */ const isValidFileType = (file, routeConfig)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runSync((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.matchFileType)(file, (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.objectKeys)(routeConfig)).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map((type)=>file.type.includes(type)), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false)));\n/**\n * Validate that a file is of a valid size given a route config\n * @public\n */ const isValidFileSize = (file, routeConfig)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runSync((0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.matchFileType)(file, (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.objectKeys)(routeConfig)).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.flatMap((type)=>(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.fileSizeToBytes)(routeConfig[type].maxFileSize)), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.map((maxFileSize)=>file.size <= maxFileSize), effect_Micro__WEBPACK_IMPORTED_MODULE_1__.orElseSucceed(()=>false)));\n/**\n * Generate a typed uploader for a given FileRouter\n * @public\n */ const genUploader = (initOpts)=>{\n    const routeRegistry = (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.createIdentityProxy)();\n    const controllableUpload = async (slug, opts)=>{\n        const uploads = new Map();\n        const endpoint = typeof slug === \"function\" ? slug(routeRegistry) : slug;\n        const utReporter = (0,_dist_internal_ut_reporter_js__WEBPACK_IMPORTED_MODULE_2__.createUTReporter)({\n            endpoint: String(endpoint),\n            package: initOpts?.package ?? \"uploadthing/client\",\n            url: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.resolveMaybeUrlArg)(initOpts?.url),\n            headers: opts.headers\n        });\n        const fetchFn = initOpts?.fetch ?? window.fetch;\n        const presigneds = await effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runPromise(utReporter(\"upload\", {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n            input: \"input\" in opts ? opts.input : null,\n            files: opts.files.map((f)=>({\n                    name: f.name,\n                    size: f.size,\n                    type: f.type,\n                    lastModified: f.lastModified\n                }))\n        }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.provideService(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext, fetchFn)));\n        const totalSize = opts.files.reduce((acc, f)=>acc + f.size, 0);\n        let totalLoaded = 0;\n        const uploadEffect = (file, presigned)=>(0,_dist_internal_upload_browser_js__WEBPACK_IMPORTED_MODULE_3__.uploadFile)(file, presigned, {\n                onUploadProgress: (progressEvent)=>{\n                    totalLoaded += progressEvent.delta;\n                    opts.onUploadProgress?.({\n                        ...progressEvent,\n                        file,\n                        progress: Math.round(progressEvent.loaded / file.size * 100),\n                        totalLoaded,\n                        totalProgress: Math.round(totalLoaded / totalSize * 100)\n                    });\n                }\n            }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.provideService(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext, fetchFn));\n        for (const [i, p] of presigneds.entries()){\n            const file = opts.files[i];\n            if (!file) continue;\n            const deferred = (0,_dist_internal_deferred_js__WEBPACK_IMPORTED_MODULE_4__.createDeferred)();\n            uploads.set(file, {\n                deferred,\n                presigned: p\n            });\n            void effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runPromiseExit(uploadEffect(file, p), {\n                signal: deferred.ac.signal\n            }).then((result)=>{\n                if (result._tag === \"Success\") {\n                    return deferred.resolve(result.value);\n                } else if (result.cause._tag === \"Interrupt\") {\n                    throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadPausedError();\n                }\n                throw effect_Micro__WEBPACK_IMPORTED_MODULE_1__.causeSquash(result.cause);\n            }).catch((err)=>{\n                if (err instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadPausedError) return;\n                deferred.reject(err);\n            });\n        }\n        /**\n     * Pause an ongoing upload\n     * @param file The file upload you want to pause. Can be omitted to pause all files\n     */ const pauseUpload = (file)=>{\n            const files = effect_Array__WEBPACK_IMPORTED_MODULE_5__.ensure(file ?? opts.files);\n            for (const file of files){\n                const upload = uploads.get(file);\n                if (!upload) return;\n                if (upload.deferred.ac.signal.aborted) {\n                    // Cancel the upload if it's already been paused\n                    throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadAbortedError();\n                }\n                upload.deferred.ac.abort();\n            }\n        };\n        /**\n     * Resume a paused upload\n     * @param file The file upload you want to resume. Can be omitted to resume all files\n     */ const resumeUpload = (file)=>{\n            const files = effect_Array__WEBPACK_IMPORTED_MODULE_5__.ensure(file ?? opts.files);\n            for (const file of files){\n                const upload = uploads.get(file);\n                if (!upload) throw \"No upload found\";\n                upload.deferred.ac = new AbortController();\n                void effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runPromiseExit(uploadEffect(file, upload.presigned), {\n                    signal: upload.deferred.ac.signal\n                }).then((result)=>{\n                    if (result._tag === \"Success\") {\n                        return upload.deferred.resolve(result.value);\n                    } else if (result.cause._tag === \"Interrupt\") {\n                        throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadPausedError();\n                    }\n                    throw effect_Micro__WEBPACK_IMPORTED_MODULE_1__.causeSquash(result.cause);\n                }).catch((err)=>{\n                    if (err instanceof _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadPausedError) return;\n                    upload.deferred.reject(err);\n                });\n            }\n        };\n        /**\n     * Wait for an upload to complete\n     * @param file The file upload you want to wait for. Can be omitted to wait for all files\n     */ const done = async (file)=>{\n            const promises = [];\n            const files = effect_Array__WEBPACK_IMPORTED_MODULE_5__.ensure(file ?? opts.files);\n            for (const file of files){\n                const upload = uploads.get(file);\n                if (!upload) throw \"No upload found\";\n                promises.push(upload.deferred.promise);\n            }\n            const results = await Promise.all(promises);\n            return file ? results[0] : results;\n        };\n        return {\n            pauseUpload,\n            resumeUpload,\n            done\n        };\n    };\n    /**\n   * One step upload function that both requests presigned URLs\n   * and then uploads the files to UploadThing\n   */ const typedUploadFiles = (slug, opts)=>{\n        const endpoint = typeof slug === \"function\" ? slug(routeRegistry) : slug;\n        const fetchFn = initOpts?.fetch ?? window.fetch;\n        return (0,_dist_internal_upload_browser_js__WEBPACK_IMPORTED_MODULE_3__.uploadFilesInternal)(endpoint, {\n            ...opts,\n            skipPolling: {},\n            url: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.resolveMaybeUrlArg)(initOpts?.url),\n            package: initOpts?.package ?? \"uploadthing/client\",\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-member-access\n            input: opts.input\n        }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_1__.provideService(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.FetchContext, fetchFn), (effect)=>effect_Micro__WEBPACK_IMPORTED_MODULE_1__.runPromiseExit(effect, opts.signal && {\n                signal: opts.signal\n            })).then((exit)=>{\n            if (exit._tag === \"Success\") {\n                return exit.value;\n            } else if (exit.cause._tag === \"Interrupt\") {\n                throw new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_0__.UploadAbortedError();\n            }\n            throw effect_Micro__WEBPACK_IMPORTED_MODULE_1__.causeSquash(exit.cause);\n        });\n    };\n    return {\n        uploadFiles: typedUploadFiles,\n        createUpload: controllableUpload,\n        /**\n     * Identity object that can be used instead of raw strings\n     * that allows \"Go to definition\" in your IDE to bring you\n     * to the backend definition of a route.\n     */ routeRegistry\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uploadthing/client/index.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/uploadthing/dist/_internal/deferred.js":
/*!*************************************************************!*\
  !*** ./node_modules/uploadthing/dist/_internal/deferred.js ***!
  \*************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createDeferred: () => (/* binding */ createDeferred)\n/* harmony export */ });\nconst createDeferred = ()=>{\n    let resolve;\n    let reject;\n    const ac = new AbortController();\n    const promise = new Promise((res, rej)=>{\n        resolve = res;\n        reject = rej;\n    });\n    return {\n        promise,\n        ac,\n        resolve,\n        reject\n    };\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXBsb2FkdGhpbmcvZGlzdC9faW50ZXJuYWwvZGVmZXJyZWQuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUFBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EsS0FBSztBQUNMO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUUwQiIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBaG1lZFxcRGVza3RvcFxcY29kZVxcYm9vdFxcZGFzaGJvYXJkXFxub2RlX21vZHVsZXNcXHVwbG9hZHRoaW5nXFxkaXN0XFxfaW50ZXJuYWxcXGRlZmVycmVkLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGNyZWF0ZURlZmVycmVkID0gKCk9PntcbiAgICBsZXQgcmVzb2x2ZTtcbiAgICBsZXQgcmVqZWN0O1xuICAgIGNvbnN0IGFjID0gbmV3IEFib3J0Q29udHJvbGxlcigpO1xuICAgIGNvbnN0IHByb21pc2UgPSBuZXcgUHJvbWlzZSgocmVzLCByZWopPT57XG4gICAgICAgIHJlc29sdmUgPSByZXM7XG4gICAgICAgIHJlamVjdCA9IHJlajtcbiAgICB9KTtcbiAgICByZXR1cm4ge1xuICAgICAgICBwcm9taXNlLFxuICAgICAgICBhYyxcbiAgICAgICAgcmVzb2x2ZSxcbiAgICAgICAgcmVqZWN0XG4gICAgfTtcbn07XG5cbmV4cG9ydCB7IGNyZWF0ZURlZmVycmVkIH07XG4iXSwibmFtZXMiOltdLCJpZ25vcmVMaXN0IjpbMF0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uploadthing/dist/_internal/deferred.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/uploadthing/dist/_internal/deprecations.js":
/*!*****************************************************************!*\
  !*** ./node_modules/uploadthing/dist/_internal/deprecations.js ***!
  \*****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   logDeprecationWarning: () => (/* binding */ logDeprecationWarning)\n/* harmony export */ });\nconst logDeprecationWarning = (message)=>{\n    // eslint-disable-next-line no-console\n    console.warn(`⚠️ [uploadthing][deprecated] ${message}`);\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvdXBsb2FkdGhpbmcvZGlzdC9faW50ZXJuYWwvZGVwcmVjYXRpb25zLmpzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTtBQUNBO0FBQ0EsaURBQWlELFFBQVE7QUFDekQ7O0FBRWlDIiwic291cmNlcyI6WyJDOlxcVXNlcnNcXEFobWVkXFxEZXNrdG9wXFxjb2RlXFxib290XFxkYXNoYm9hcmRcXG5vZGVfbW9kdWxlc1xcdXBsb2FkdGhpbmdcXGRpc3RcXF9pbnRlcm5hbFxcZGVwcmVjYXRpb25zLmpzIl0sInNvdXJjZXNDb250ZW50IjpbImNvbnN0IGxvZ0RlcHJlY2F0aW9uV2FybmluZyA9IChtZXNzYWdlKT0+e1xuICAgIC8vIGVzbGludC1kaXNhYmxlLW5leHQtbGluZSBuby1jb25zb2xlXG4gICAgY29uc29sZS53YXJuKGDimqDvuI8gW3VwbG9hZHRoaW5nXVtkZXByZWNhdGVkXSAke21lc3NhZ2V9YCk7XG59O1xuXG5leHBvcnQgeyBsb2dEZXByZWNhdGlvbldhcm5pbmcgfTtcbiJdLCJuYW1lcyI6W10sImlnbm9yZUxpc3QiOlswXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uploadthing/dist/_internal/deprecations.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/uploadthing/dist/_internal/upload-browser.js":
/*!*******************************************************************!*\
  !*** ./node_modules/uploadthing/dist/_internal/upload-browser.js ***!
  \*******************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   uploadFile: () => (/* binding */ uploadFile),\n/* harmony export */   uploadFilesInternal: () => (/* binding */ uploadFilesInternal)\n/* harmony export */ });\n/* harmony import */ var effect_Function__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! effect/Function */ \"(ssr)/./node_modules/effect/dist/esm/Function.js\");\n/* harmony import */ var effect_Micro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/Micro */ \"(ssr)/./node_modules/effect/dist/esm/Micro.js\");\n/* harmony import */ var effect_Predicate__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! effect/Predicate */ \"(ssr)/./node_modules/effect/dist/esm/Predicate.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @uploadthing/shared */ \"(ssr)/./node_modules/@uploadthing/shared/dist/index.js\");\n/* harmony import */ var _deprecations_js__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! ./deprecations.js */ \"(ssr)/./node_modules/uploadthing/dist/_internal/deprecations.js\");\n/* harmony import */ var _ut_reporter_js__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! ./ut-reporter.js */ \"(ssr)/./node_modules/uploadthing/dist/_internal/ut-reporter.js\");\n\n\n\n\n\n\n\nvar version = \"7.7.2\";\n\nconst uploadWithProgress = (file, rangeStart, presigned, onUploadProgress)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.async((resume)=>{\n        const xhr = new XMLHttpRequest();\n        xhr.open(\"PUT\", presigned.url, true);\n        xhr.setRequestHeader(\"Range\", `bytes=${rangeStart}-`);\n        xhr.setRequestHeader(\"x-uploadthing-version\", version);\n        xhr.responseType = \"json\";\n        let previousLoaded = 0;\n        xhr.upload.addEventListener(\"progress\", ({ loaded })=>{\n            const delta = loaded - previousLoaded;\n            onUploadProgress?.({\n                loaded,\n                delta\n            });\n            previousLoaded = loaded;\n        });\n        xhr.addEventListener(\"load\", ()=>{\n            if (xhr.status >= 200 && xhr.status < 300 && (0,effect_Predicate__WEBPACK_IMPORTED_MODULE_1__.isRecord)(xhr.response)) {\n                if ((0,effect_Predicate__WEBPACK_IMPORTED_MODULE_1__.hasProperty)(xhr.response, \"error\")) {\n                    resume(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.UploadThingError({\n                        code: \"UPLOAD_FAILED\",\n                        message: String(xhr.response.error),\n                        data: xhr.response\n                    }));\n                } else {\n                    resume(effect_Micro__WEBPACK_IMPORTED_MODULE_0__.succeed(xhr.response));\n                }\n            } else {\n                resume(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.UploadThingError({\n                    code: \"UPLOAD_FAILED\",\n                    message: `XHR failed ${xhr.status} ${xhr.statusText}`,\n                    data: xhr.response\n                }));\n            }\n        });\n        // Is there a case when the client would throw and\n        // ingest server not knowing about it? idts?\n        xhr.addEventListener(\"error\", ()=>{\n            resume(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.UploadThingError({\n                code: \"UPLOAD_FAILED\"\n            }));\n        });\n        const formData = new FormData();\n        /**\n     * iOS/React Native FormData handling requires special attention:\n     *\n     * Issue: In React Native, iOS crashes with \"attempt to insert nil object\" when appending File directly\n     * to FormData. This happens because iOS tries to create NSDictionary from the file object and expects\n     * specific structure {uri, type, name}.\n     *\n     *\n     * Note: Don't try to use Blob or modify File object - iOS specifically needs plain object\n     * with these properties to create valid NSDictionary.\n     */ if (\"uri\" in file) {\n            // eslint-disable-next-line @typescript-eslint/no-unsafe-argument\n            formData.append(\"file\", {\n                uri: file.uri,\n                type: file.type,\n                name: file.name,\n                ...rangeStart > 0 && {\n                    range: rangeStart\n                }\n            });\n        } else {\n            formData.append(\"file\", rangeStart > 0 ? file.slice(rangeStart) : file);\n        }\n        xhr.send(formData);\n        return effect_Micro__WEBPACK_IMPORTED_MODULE_0__.sync(()=>xhr.abort());\n    });\nconst uploadFile = (file, presigned, opts)=>(0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_2__.fetchEff)(presigned.url, {\n        method: \"HEAD\"\n    }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_0__.map(({ headers })=>parseInt(headers.get(\"x-ut-range-start\") ?? \"0\", 10)), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.tap((start)=>opts.onUploadProgress?.({\n            delta: start,\n            loaded: start\n        })), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.flatMap((start)=>uploadWithProgress(file, start, presigned, (progressEvent)=>opts.onUploadProgress?.({\n                delta: progressEvent.delta,\n                loaded: progressEvent.loaded + start\n            }))), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.map(effect_Function__WEBPACK_IMPORTED_MODULE_3__.unsafeCoerce), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.map((uploadResponse)=>({\n            name: file.name,\n            size: file.size,\n            key: presigned.key,\n            lastModified: file.lastModified,\n            serverData: uploadResponse.serverData,\n            get url () {\n                (0,_deprecations_js__WEBPACK_IMPORTED_MODULE_4__.logDeprecationWarning)(\"`file.url` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.\");\n                return uploadResponse.url;\n            },\n            get appUrl () {\n                (0,_deprecations_js__WEBPACK_IMPORTED_MODULE_4__.logDeprecationWarning)(\"`file.appUrl` is deprecated and will be removed in uploadthing v9. Use `file.ufsUrl` instead.\");\n                return uploadResponse.appUrl;\n            },\n            ufsUrl: uploadResponse.ufsUrl,\n            customId: presigned.customId,\n            type: file.type,\n            fileHash: uploadResponse.fileHash\n        })));\nconst uploadFilesInternal = (endpoint, opts)=>{\n    // classic service right here\n    const reportEventToUT = (0,_ut_reporter_js__WEBPACK_IMPORTED_MODULE_5__.createUTReporter)({\n        endpoint: String(endpoint),\n        package: opts.package,\n        url: opts.url,\n        headers: opts.headers\n    });\n    const totalSize = opts.files.reduce((acc, f)=>acc + f.size, 0);\n    let totalLoaded = 0;\n    return effect_Micro__WEBPACK_IMPORTED_MODULE_0__.flatMap(reportEventToUT(\"upload\", {\n        input: \"input\" in opts ? opts.input : null,\n        files: opts.files.map((f)=>({\n                name: f.name,\n                size: f.size,\n                type: f.type,\n                lastModified: f.lastModified\n            }))\n    }), (presigneds)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.forEach(presigneds, (presigned, i)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.flatMap(effect_Micro__WEBPACK_IMPORTED_MODULE_0__.sync(()=>opts.onUploadBegin?.({\n                    file: opts.files[i].name\n                })), ()=>uploadFile(opts.files[i], presigned, {\n                    onUploadProgress: (ev)=>{\n                        totalLoaded += ev.delta;\n                        opts.onUploadProgress?.({\n                            file: opts.files[i],\n                            progress: ev.loaded / opts.files[i].size * 100,\n                            loaded: ev.loaded,\n                            delta: ev.delta,\n                            totalLoaded,\n                            totalProgress: totalLoaded / totalSize\n                        });\n                    }\n                })), {\n            concurrency: 6\n        }));\n};\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uploadthing/dist/_internal/upload-browser.js\n");

/***/ }),

/***/ "(ssr)/./node_modules/uploadthing/dist/_internal/ut-reporter.js":
/*!****************************************************************!*\
  !*** ./node_modules/uploadthing/dist/_internal/ut-reporter.js ***!
  \****************************************************************/
/***/ ((__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   createUTReporter: () => (/* binding */ createUTReporter)\n/* harmony export */ });\n/* harmony import */ var effect_Function__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! effect/Function */ \"(ssr)/./node_modules/effect/dist/esm/Function.js\");\n/* harmony import */ var effect_Micro__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! effect/Micro */ \"(ssr)/./node_modules/effect/dist/esm/Micro.js\");\n/* harmony import */ var _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @uploadthing/shared */ \"(ssr)/./node_modules/@uploadthing/shared/dist/index.js\");\n\n\n\n\nvar version = \"7.7.2\";\n\nconst createAPIRequestUrl = (config)=>{\n    const url = new URL(config.url);\n    const queryParams = new URLSearchParams(url.search);\n    queryParams.set(\"actionType\", config.actionType);\n    queryParams.set(\"slug\", config.slug);\n    url.search = queryParams.toString();\n    return url;\n};\n/**\n * Creates a \"client\" for reporting events to the UploadThing server via the user's API endpoint.\n * Events are handled in \"./handler.ts starting at L112\"\n */ const createUTReporter = (cfg)=>(type, payload)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.gen(function*() {\n            const url = createAPIRequestUrl({\n                url: cfg.url,\n                slug: cfg.endpoint,\n                actionType: type\n            });\n            const headers = new Headers((yield* effect_Micro__WEBPACK_IMPORTED_MODULE_0__.promise(async ()=>typeof cfg.headers === \"function\" ? await cfg.headers() : cfg.headers)));\n            if (cfg.package) {\n                headers.set(\"x-uploadthing-package\", cfg.package);\n            }\n            headers.set(\"x-uploadthing-version\", version);\n            headers.set(\"Content-Type\", \"application/json\");\n            const response = yield* (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.fetchEff)(url, {\n                method: \"POST\",\n                body: JSON.stringify(payload),\n                headers\n            }).pipe(effect_Micro__WEBPACK_IMPORTED_MODULE_0__.andThen(_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.parseResponseJson), /**\n         * We don't _need_ to validate the response here, just cast it for now.\n         * As of now, @effect/schema includes quite a few bytes we cut out by this...\n         * We have \"strong typing\" on the backend that ensures the shape should match.\n         */ effect_Micro__WEBPACK_IMPORTED_MODULE_0__.map(effect_Function__WEBPACK_IMPORTED_MODULE_2__.unsafeCoerce), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.catchTag(\"FetchError\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.UploadThingError({\n                    code: \"INTERNAL_CLIENT_ERROR\",\n                    message: `Failed to report event \"${type}\" to UploadThing server`,\n                    cause: e\n                }))), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.catchTag(\"BadRequestError\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.UploadThingError({\n                    code: (0,_uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.getErrorTypeFromStatusCode)(e.status),\n                    message: e.getMessage(),\n                    cause: e.json\n                }))), effect_Micro__WEBPACK_IMPORTED_MODULE_0__.catchTag(\"InvalidJson\", (e)=>effect_Micro__WEBPACK_IMPORTED_MODULE_0__.fail(new _uploadthing_shared__WEBPACK_IMPORTED_MODULE_1__.UploadThingError({\n                    code: \"INTERNAL_CLIENT_ERROR\",\n                    message: \"Failed to parse response from UploadThing server\",\n                    cause: e\n                }))));\n            return response;\n        });\n\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/uploadthing/dist/_internal/ut-reporter.js\n");

/***/ })

};
;