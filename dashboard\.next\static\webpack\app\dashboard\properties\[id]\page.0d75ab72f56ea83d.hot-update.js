"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/[id]/page.tsx":
/*!************************************************!*\
  !*** ./app/dashboard/properties/[id]/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PropertyShowPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _services_propertyService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/propertyService */ \"(app-pages-browser)/./services/propertyService.ts\");\n/* harmony import */ var _components_properties_PropertyShowComponent__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/properties/PropertyShowComponent */ \"(app-pages-browser)/./components/properties/PropertyShowComponent.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _styles_arabic_properties_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/styles/arabic-properties.css */ \"(app-pages-browser)/./styles/arabic-properties.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction PropertyShowPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [property, setProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const propertyId = params.id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyShowPage.useEffect\": ()=>{\n            if (propertyId) {\n                fetchProperty();\n            }\n        }\n    }[\"PropertyShowPage.useEffect\"], [\n        propertyId\n    ]);\n    const fetchProperty = async ()=>{\n        try {\n            setIsLoading(true);\n            const data = await _services_propertyService__WEBPACK_IMPORTED_MODULE_4__.propertyService.getPropertyById(propertyId);\n            setProperty(data);\n        } catch (error) {\n            console.error('Error fetching property:', error);\n            // Fallback to mock data if backend is not available\n            console.log('Backend not available, using mock data for property:', propertyId);\n            const mockProperty = {\n                id: propertyId,\n                title: 'فيلا فاخرة في دبي مارينا',\n                titleAr: 'فيلا فاخرة في دبي مارينا',\n                description: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة',\n                descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة',\n                price: 2500000,\n                currency: 'AED',\n                type: 'VILLA',\n                status: 'AVAILABLE',\n                bedrooms: 4,\n                bathrooms: 3,\n                area: 350,\n                location: 'دبي مارينا',\n                locationAr: 'دبي مارينا',\n                address: '123 ممشى المارينا',\n                addressAr: '123 ممشى المارينا',\n                city: 'دبي',\n                cityAr: 'دبي',\n                country: 'الإمارات العربية المتحدة',\n                countryAr: 'الإمارات العربية المتحدة',\n                latitude: 25.0772,\n                longitude: 55.1395,\n                images: [\n                    'https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop'\n                ],\n                features: [\n                    'مسبح خاص',\n                    'حديقة',\n                    'موقف سيارات',\n                    'أمن 24/7'\n                ],\n                featuresAr: [\n                    'مسبح خاص',\n                    'حديقة',\n                    'موقف سيارات',\n                    'أمن 24/7'\n                ],\n                amenities: [\n                    'صالة رياضية',\n                    'سبا',\n                    'ملعب تنس',\n                    'مارينا خاصة'\n                ],\n                amenitiesAr: [\n                    'صالة رياضية',\n                    'سبا',\n                    'ملعب تنس',\n                    'مارينا خاصة'\n                ],\n                yearBuilt: 2020,\n                parking: 2,\n                furnished: true,\n                petFriendly: false,\n                utilities: 'جميع المرافق متضمنة',\n                utilitiesAr: 'جميع المرافق متضمنة',\n                contactInfo: '+971 50 123 4567',\n                agentId: 'agent1',\n                isActive: true,\n                isFeatured: true,\n                viewCount: 125,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n                agent: {\n                    id: 'agent1',\n                    name: 'أحمد محمد',\n                    email: '<EMAIL>'\n                }\n            };\n            setProperty(mockProperty);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEdit = ()=>{\n        router.push(\"/dashboard/properties/\".concat(propertyId, \"/edit\"));\n    };\n    const handleDelete = async ()=>{\n        if (!confirm('هل أنت متأكد من حذف هذا العقار؟')) {\n            return;\n        }\n        try {\n            setIsDeleting(true);\n            await _services_propertyService__WEBPACK_IMPORTED_MODULE_4__.propertyService.deleteProperty(propertyId);\n            toast({\n                title: 'تم حذف العقار',\n                description: 'تم حذف العقار بنجاح'\n            });\n            router.push('/dashboard/properties');\n        } catch (error) {\n            console.error('Error deleting property:', error);\n            toast({\n                title: 'خطأ في حذف العقار',\n                description: 'حدث خطأ أثناء حذف العقار',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const handleBack = ()=>{\n        router.push('/dashboard/properties');\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen property-form-dark rtl arabic-text\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-[400px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 187,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-3 text-lg\",\n                            children: \"جاري تحميل العقار...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 188,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 186,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 185,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 184,\n            columnNumber: 7\n        }, this);\n    }\n    if (!property) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen property-form-dark rtl arabic-text\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-red-400 mb-4\",\n                            children: \"العقار غير موجود\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 200,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-6\",\n                            children: \"لم يتم العثور على العقار المطلوب\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            onClick: handleBack,\n                            className: \"btn-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 203,\n                                    columnNumber: 15\n                                }, this),\n                                \"العودة إلى قائمة العقارات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 202,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 199,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 198,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 197,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen property-form-dark rtl arabic-text\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"property-header-dark mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleBack,\n                                        variant: \"ghost\",\n                                        className: \"text-gray-400 hover:text-white mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 224,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"العودة إلى قائمة العقارات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 219,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"arabic-heading text-3xl font-bold text-white\",\n                                        children: property.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mt-2\",\n                                        children: [\n                                            property.location,\n                                            \" • \",\n                                            property.city,\n                                            \" • \",\n                                            property.country\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 218,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleEdit,\n                                        className: \"btn-secondary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 240,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تعديل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleDelete,\n                                        disabled: isDeleting,\n                                        className: \"bg-red-600 hover:bg-red-700 text-white\",\n                                        children: [\n                                            isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"loading-spinner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 248,\n                                                columnNumber: 32\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 249,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"حذف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 243,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 235,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                        lineNumber: 217,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 216,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_properties_PropertyShowComponent__WEBPACK_IMPORTED_MODULE_5__.PropertyShowComponent, {\n                    property: property\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 257,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 214,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n        lineNumber: 213,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyShowPage, \"iRGGcDe76PMT/k9AlYn3/VeU8tI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = PropertyShowPage;\nvar _c;\n$RefreshReg$(_c, \"PropertyShowPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/[id]/page.tsx\n"));

/***/ })

});