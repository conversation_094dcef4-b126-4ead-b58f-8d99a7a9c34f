"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/[id]/page.tsx":
/*!************************************************!*\
  !*** ./app/dashboard/properties/[id]/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PropertyShowPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _services_propertyService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/propertyService */ \"(app-pages-browser)/./services/propertyService.ts\");\n/* harmony import */ var _components_properties_PropertyShowComponent__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/properties/PropertyShowComponent */ \"(app-pages-browser)/./components/properties/PropertyShowComponent.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _styles_arabic_properties_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/styles/arabic-properties.css */ \"(app-pages-browser)/./styles/arabic-properties.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction PropertyShowPage() {\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [property, setProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const propertyId = params.id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyShowPage.useEffect\": ()=>{\n            if (propertyId) {\n                fetchProperty();\n            }\n        }\n    }[\"PropertyShowPage.useEffect\"], [\n        propertyId\n    ]);\n    const fetchProperty = async ()=>{\n        try {\n            setIsLoading(true);\n            console.log('Fetching property:', propertyId);\n            // For now, always use mock data since backend might not be available\n            console.log('Using mock data for property:', propertyId);\n            const mockProperty = {\n                id: propertyId,\n                title: 'فيلا فاخرة في دبي مارينا',\n                titleAr: 'فيلا فاخرة في دبي مارينا',\n                description: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة',\n                descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة',\n                price: 2500000,\n                currency: 'AED',\n                type: 'VILLA',\n                status: 'AVAILABLE',\n                bedrooms: 4,\n                bathrooms: 3,\n                area: 350,\n                location: 'دبي مارينا',\n                locationAr: 'دبي مارينا',\n                address: '123 ممشى المارينا',\n                addressAr: '123 ممشى المارينا',\n                city: 'دبي',\n                cityAr: 'دبي',\n                country: 'الإمارات العربية المتحدة',\n                countryAr: 'الإمارات العربية المتحدة',\n                latitude: 25.0772,\n                longitude: 55.1395,\n                images: [\n                    'https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop'\n                ],\n                features: [\n                    'مسبح خاص',\n                    'حديقة',\n                    'موقف سيارات',\n                    'أمن 24/7'\n                ],\n                featuresAr: [\n                    'مسبح خاص',\n                    'حديقة',\n                    'موقف سيارات',\n                    'أمن 24/7'\n                ],\n                amenities: [\n                    'صالة رياضية',\n                    'سبا',\n                    'ملعب تنس',\n                    'مارينا خاصة'\n                ],\n                amenitiesAr: [\n                    'صالة رياضية',\n                    'سبا',\n                    'ملعب تنس',\n                    'مارينا خاصة'\n                ],\n                yearBuilt: 2020,\n                parking: 2,\n                furnished: true,\n                petFriendly: false,\n                utilities: 'جميع المرافق متضمنة',\n                utilitiesAr: 'جميع المرافق متضمنة',\n                contactInfo: '+971 50 123 4567',\n                agentId: 'agent1',\n                isActive: true,\n                isFeatured: true,\n                viewCount: 125,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n                agent: {\n                    id: 'agent1',\n                    name: 'أحمد محمد',\n                    email: '<EMAIL>'\n                }\n            };\n            setProperty(mockProperty);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEdit = ()=>{\n        router.push(\"/dashboard/properties/\".concat(propertyId, \"/edit\"));\n    };\n    const handleDelete = async ()=>{\n        if (!confirm('هل أنت متأكد من حذف هذا العقار؟')) {\n            return;\n        }\n        try {\n            setIsDeleting(true);\n            await _services_propertyService__WEBPACK_IMPORTED_MODULE_4__.propertyService.deleteProperty(propertyId);\n            toast({\n                title: 'تم حذف العقار',\n                description: 'تم حذف العقار بنجاح'\n            });\n            router.push('/dashboard/properties');\n        } catch (error) {\n            console.error('Error deleting property:', error);\n            toast({\n                title: 'خطأ في حذف العقار',\n                description: 'حدث خطأ أثناء حذف العقار',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const handleBack = ()=>{\n        router.push('/dashboard/properties');\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen property-form-dark rtl arabic-text\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-[400px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 184,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-3 text-lg\",\n                            children: \"جاري تحميل العقار...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 185,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 183,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 182,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 181,\n            columnNumber: 7\n        }, this);\n    }\n    if (!property) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen property-form-dark rtl arabic-text\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-red-400 mb-4\",\n                            children: \"العقار غير موجود\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 197,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-6\",\n                            children: \"لم يتم العثور على العقار المطلوب\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            onClick: handleBack,\n                            className: \"btn-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 200,\n                                    columnNumber: 15\n                                }, this),\n                                \"العودة إلى قائمة العقارات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 196,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 195,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 194,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen property-form-dark rtl arabic-text\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"property-header-dark mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleBack,\n                                        variant: \"ghost\",\n                                        className: \"text-gray-400 hover:text-white mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 221,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"العودة إلى قائمة العقارات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 216,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"arabic-heading text-3xl font-bold text-white\",\n                                        children: property.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 224,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mt-2\",\n                                        children: [\n                                            property.location,\n                                            \" • \",\n                                            property.city,\n                                            \" • \",\n                                            property.country\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 227,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 215,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleEdit,\n                                        className: \"btn-secondary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 237,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تعديل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 233,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleDelete,\n                                        disabled: isDeleting,\n                                        className: \"bg-red-600 hover:bg-red-700 text-white\",\n                                        children: [\n                                            isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"loading-spinner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 245,\n                                                columnNumber: 32\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 246,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"حذف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 240,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 232,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                        lineNumber: 214,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 213,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_properties_PropertyShowComponent__WEBPACK_IMPORTED_MODULE_5__.PropertyShowComponent, {\n                    property: property\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 254,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 211,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n        lineNumber: 210,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyShowPage, \"iRGGcDe76PMT/k9AlYn3/VeU8tI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = PropertyShowPage;\nvar _c;\n$RefreshReg$(_c, \"PropertyShowPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/[id]/page.tsx\n"));

/***/ })

});