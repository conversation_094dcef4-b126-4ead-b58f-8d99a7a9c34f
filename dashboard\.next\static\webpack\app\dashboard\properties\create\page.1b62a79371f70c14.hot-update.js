"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./hooks/useSimpleLanguage.tsx":
/*!*************************************!*\
  !*** ./hooks/useSimpleLanguage.tsx ***!
  \*************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   useSimpleLanguage: () => (/* binding */ useSimpleLanguage)\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @/lib/cookieCleanup */ \"(app-pages-browser)/./lib/cookieCleanup.ts\");\n/* harmony import */ var _lib_i18n_settings__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/i18n/settings */ \"(app-pages-browser)/./lib/i18n/settings.ts\");\n/* __next_internal_client_entry_do_not_use__ useSimpleLanguage auto */ var _s = $RefreshSig$();\n\n\n\n/**\n * Arabic-only language hook for Properties system\n * Supports Arabic language with RTL text direction\n */ function useSimpleLanguage() {\n    _s();\n    const [language] = (0,react__WEBPACK_IMPORTED_MODULE_0__.useState)('ar'); // Arabic only\n    // Initialize Arabic interface with cleanup\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            // Clean up cookies first\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.cleanupCookies)();\n            // Set Arabic language preference\n            localStorage.setItem('properties-language', 'ar');\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)('🏠 Arabic Properties system initialized');\n        }\n    }[\"useSimpleLanguage.useEffect\"], []);\n    // Set Arabic document properties\n    (0,react__WEBPACK_IMPORTED_MODULE_0__.useEffect)({\n        \"useSimpleLanguage.useEffect\": ()=>{\n            // Update document properties for Arabic\n            document.documentElement.lang = 'ar';\n            document.documentElement.dir = 'rtl';\n            // Update CSS classes for Arabic\n            document.documentElement.className = 'rtl arabic-interface';\n            // Set Arabic fonts\n            document.body.style.fontFamily = \"'Cairo', 'Noto Sans Arabic', 'Tajawal', 'Amiri', sans-serif\";\n            (0,_lib_cookieCleanup__WEBPACK_IMPORTED_MODULE_1__.safeLog)('🌐 Arabic language interface active');\n        }\n    }[\"useSimpleLanguage.useEffect\"], []);\n    // Translation function for Arabic\n    const t = (key)=>{\n        try {\n            var _resources_ar;\n            const keys = key.split('.');\n            let value = (_resources_ar = _lib_i18n_settings__WEBPACK_IMPORTED_MODULE_2__.resources.ar) === null || _resources_ar === void 0 ? void 0 : _resources_ar.translation;\n            for (const k of keys){\n                value = value === null || value === void 0 ? void 0 : value[k];\n            }\n            // Debug logging\n            if (true) {\n                console.log(\"Translation: \".concat(key, \" -> \").concat(value || 'NOT FOUND'));\n            }\n            return value || key;\n        } catch (error) {\n            console.error('Translation error:', error);\n            return key;\n        }\n    };\n    return {\n        language,\n        setLanguage: ()=>{},\n        isRTL: true,\n        isArabic: true,\n        isEnglish: false,\n        t\n    };\n}\n_s(useSimpleLanguage, \"vJYXkmcagJrDfhu6Qs2lIfg6PMg=\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./hooks/useSimpleLanguage.tsx\n"));

/***/ })

});