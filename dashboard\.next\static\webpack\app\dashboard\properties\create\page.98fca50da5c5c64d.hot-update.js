"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./components/properties/PropertyCreateForm.tsx":
/*!******************************************************!*\
  !*** ./components/properties/PropertyCreateForm.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyCreateForm: () => (/* binding */ PropertyCreateForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _services_propertyService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/propertyService */ \"(app-pages-browser)/./services/propertyService.ts\");\n/* __next_internal_client_entry_do_not_use__ PropertyCreateForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction PropertyCreateForm(param) {\n    let { onSuccess, onCancel, isLoading, setIsLoading } = param;\n    _s();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        price: '',\n        currency: 'USD',\n        type: '',\n        status: 'AVAILABLE',\n        bedrooms: '',\n        bathrooms: '',\n        area: '',\n        location: '',\n        address: '',\n        city: '',\n        country: 'UAE',\n        images: [],\n        features: [],\n        amenities: [],\n        yearBuilt: '',\n        parking: '',\n        furnished: false,\n        petFriendly: false\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [imagePreviewUrls, setImagePreviewUrls] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleInputChange]\": (field, value)=>{\n            setFormData({\n                \"PropertyCreateForm.useCallback[handleInputChange]\": (prev)=>({\n                        ...prev,\n                        [field]: value\n                    })\n            }[\"PropertyCreateForm.useCallback[handleInputChange]\"]);\n            // Clear error when user starts typing\n            if (errors[field]) {\n                setErrors({\n                    \"PropertyCreateForm.useCallback[handleInputChange]\": (prev)=>({\n                            ...prev,\n                            [field]: ''\n                        })\n                }[\"PropertyCreateForm.useCallback[handleInputChange]\"]);\n            }\n        }\n    }[\"PropertyCreateForm.useCallback[handleInputChange]\"], [\n        errors\n    ]);\n    // Handle image file selection\n    const handleImageChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleImageChange]\": (files)=>{\n            if (!files) return;\n            const validFiles = [];\n            const newPreviewUrls = [];\n            Array.from(files).forEach({\n                \"PropertyCreateForm.useCallback[handleImageChange]\": (file)=>{\n                    // Validate file type\n                    if (!file.type.startsWith('image/')) {\n                        toast({\n                            title: t('images.error'),\n                            description: 'يرجى اختيار ملفات صور فقط',\n                            variant: 'destructive'\n                        });\n                        return;\n                    }\n                    // Validate file size (10MB max)\n                    if (file.size > 10 * 1024 * 1024) {\n                        toast({\n                            title: t('images.error'),\n                            description: 'حجم الصورة يجب أن يكون أقل من 10MB',\n                            variant: 'destructive'\n                        });\n                        return;\n                    }\n                    validFiles.push(file);\n                    newPreviewUrls.push(URL.createObjectURL(file));\n                }\n            }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n            if (validFiles.length > 0) {\n                setFormData({\n                    \"PropertyCreateForm.useCallback[handleImageChange]\": (prev)=>({\n                            ...prev,\n                            images: [\n                                ...prev.images,\n                                ...validFiles\n                            ]\n                        })\n                }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n                setImagePreviewUrls({\n                    \"PropertyCreateForm.useCallback[handleImageChange]\": (prev)=>[\n                            ...prev,\n                            ...newPreviewUrls\n                        ]\n                }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n            }\n        }\n    }[\"PropertyCreateForm.useCallback[handleImageChange]\"], [\n        toast,\n        t\n    ]);\n    // Remove image\n    const removeImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[removeImage]\": (index)=>{\n            setFormData({\n                \"PropertyCreateForm.useCallback[removeImage]\": (prev)=>({\n                        ...prev,\n                        images: prev.images.filter({\n                            \"PropertyCreateForm.useCallback[removeImage]\": (_, i)=>i !== index\n                        }[\"PropertyCreateForm.useCallback[removeImage]\"])\n                    })\n            }[\"PropertyCreateForm.useCallback[removeImage]\"]);\n            // Revoke URL to prevent memory leaks\n            URL.revokeObjectURL(imagePreviewUrls[index]);\n            setImagePreviewUrls({\n                \"PropertyCreateForm.useCallback[removeImage]\": (prev)=>prev.filter({\n                        \"PropertyCreateForm.useCallback[removeImage]\": (_, i)=>i !== index\n                    }[\"PropertyCreateForm.useCallback[removeImage]\"])\n            }[\"PropertyCreateForm.useCallback[removeImage]\"]);\n        }\n    }[\"PropertyCreateForm.useCallback[removeImage]\"], [\n        imagePreviewUrls\n    ]);\n    // Handle drag and drop\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n        }\n    }[\"PropertyCreateForm.useCallback[handleDragOver]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            handleImageChange(e.dataTransfer.files);\n        }\n    }[\"PropertyCreateForm.useCallback[handleDrop]\"], [\n        handleImageChange\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Required fields validation\n        if (!formData.title.trim()) newErrors.title = t('validation.required');\n        if (!formData.description.trim()) newErrors.description = t('validation.required');\n        if (!formData.price || formData.price <= 0) newErrors.price = t('validation.positive');\n        if (!formData.type) newErrors.type = t('validation.required');\n        if (!formData.location.trim()) newErrors.location = t('validation.required');\n        if (!formData.address.trim()) newErrors.address = t('validation.required');\n        if (!formData.city.trim()) newErrors.city = t('validation.required');\n        if (!formData.bedrooms || formData.bedrooms < 0) newErrors.bedrooms = t('validation.positive');\n        if (!formData.bathrooms || formData.bathrooms < 0) newErrors.bathrooms = t('validation.positive');\n        if (!formData.area || formData.area <= 0) newErrors.area = t('validation.positive');\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            toast({\n                title: t('properties.error'),\n                description: t('validation.required'),\n                variant: 'destructive'\n            });\n            return;\n        }\n        setIsLoading(true);\n        setUploadingImages(true);\n        try {\n            // Upload images first if any\n            let imageUrls = [];\n            if (formData.images.length > 0) {\n                toast({\n                    title: t('images.uploading'),\n                    description: 'جاري رفع الصور...'\n                });\n                // Create FormData for image upload\n                const imageFormData = new FormData();\n                formData.images.forEach((file, index)=>{\n                    imageFormData.append(\"images\", file);\n                });\n                // Here you would typically upload to your image service\n                // For now, we'll create mock URLs\n                imageUrls = formData.images.map((file, index)=>\"https://example.com/images/\".concat(Date.now(), \"-\").concat(index, \"-\").concat(file.name));\n            }\n            const propertyData = {\n                title: formData.title,\n                description: formData.description,\n                price: Number(formData.price),\n                currency: formData.currency,\n                type: formData.type,\n                status: formData.status,\n                bedrooms: Number(formData.bedrooms),\n                bathrooms: Number(formData.bathrooms),\n                area: Number(formData.area),\n                location: formData.location,\n                address: formData.address,\n                city: formData.city,\n                country: formData.country,\n                images: imageUrls,\n                features: formData.features,\n                amenities: formData.amenities,\n                yearBuilt: formData.yearBuilt ? Number(formData.yearBuilt) : undefined,\n                parking: formData.parking ? Number(formData.parking) : undefined,\n                furnished: formData.furnished,\n                petFriendly: formData.petFriendly\n            };\n            await _services_propertyService__WEBPACK_IMPORTED_MODULE_9__.propertyService.createProperty(propertyData);\n            toast({\n                title: t('properties.success'),\n                description: 'تم إنشاء العقار بنجاح'\n            });\n            onSuccess();\n        } catch (error) {\n            console.error('Error creating property:', error);\n            toast({\n                title: t('properties.error'),\n                description: 'حدث خطأ أثناء إنشاء العقار',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsLoading(false);\n            setUploadingImages(false);\n        }\n    };\n    const propertyTypes = [\n        {\n            value: 'APARTMENT',\n            label: t('property.type.apartment')\n        },\n        {\n            value: 'VILLA',\n            label: t('property.type.villa')\n        },\n        {\n            value: 'TOWNHOUSE',\n            label: t('property.type.townhouse')\n        },\n        {\n            value: 'PENTHOUSE',\n            label: t('property.type.penthouse')\n        },\n        {\n            value: 'STUDIO',\n            label: t('property.type.studio')\n        },\n        {\n            value: 'OFFICE',\n            label: t('property.type.office')\n        },\n        {\n            value: 'SHOP',\n            label: t('property.type.shop')\n        },\n        {\n            value: 'WAREHOUSE',\n            label: t('property.type.warehouse')\n        },\n        {\n            value: 'LAND',\n            label: t('property.type.land')\n        },\n        {\n            value: 'BUILDING',\n            label: t('property.type.building')\n        }\n    ];\n    const propertyStatuses = [\n        {\n            value: 'AVAILABLE',\n            label: t('property.status.available')\n        },\n        {\n            value: 'SOLD',\n            label: t('property.status.sold')\n        },\n        {\n            value: 'RENTED',\n            label: t('property.status.rented')\n        },\n        {\n            value: 'PENDING',\n            label: t('property.status.pending')\n        }\n    ];\n    const countries = [\n        {\n            value: 'UAE',\n            label: t('country.uae')\n        },\n        {\n            value: 'SAUDI',\n            label: t('country.saudi')\n        },\n        {\n            value: 'QATAR',\n            label: t('country.qatar')\n        },\n        {\n            value: 'KUWAIT',\n            label: t('country.kuwait')\n        },\n        {\n            value: 'BAHRAIN',\n            label: t('country.bahrain')\n        },\n        {\n            value: 'OMAN',\n            label: t('country.oman')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: language === 'ar' ? 'arabic-heading' : '',\n                        children: language === 'ar' ? 'المعلومات الأساسية' : 'Basic Information'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.title'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.title ? 'error' : ''),\n                                        value: formData.title,\n                                        onChange: (e)=>handleInputChange('title', e.target.value),\n                                        placeholder: t('property.title.placeholder'),\n                                        dir: language === 'ar' ? 'rtl' : 'ltr'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 289,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 296,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 287,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: language === 'ar' ? 'العنوان بالعربية' : 'Title (Arabic)'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        value: formData.titleAr,\n                                        onChange: (e)=>handleInputChange('titleAr', e.target.value),\n                                        placeholder: \"أدخل عنوان العقار بالعربية\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 301,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 299,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 286,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.price'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.price ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.price,\n                                        onChange: (e)=>handleInputChange('price', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: t('property.price.placeholder')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.price\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 321,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.type'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 325,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.type,\n                                        onValueChange: (value)=>handleInputChange('type', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select \".concat(errors.type ? 'error' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: language === 'ar' ? 'اختر نوع العقار' : 'Select property type'\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 328,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 327,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: propertyTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: type.value,\n                                                        children: type.label\n                                                    }, type.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 332,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 330,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.type\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 338,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 324,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.status')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.status,\n                                        onValueChange: (value)=>handleInputChange('status', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 345,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 344,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: propertyStatuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: status.value,\n                                                        children: status.label\n                                                    }, status.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 349,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 347,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 343,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 341,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: language === 'ar' ? 'arabic-heading' : '',\n                        children: language === 'ar' ? 'تفاصيل العقار' : 'Property Details'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 361,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.bedrooms'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.bedrooms ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.bedrooms,\n                                        onChange: (e)=>handleInputChange('bedrooms', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"0\",\n                                        min: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.bedrooms\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.bathrooms'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 380,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.bathrooms ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.bathrooms,\n                                        onChange: (e)=>handleInputChange('bathrooms', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"0\",\n                                        min: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.bathrooms\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 34\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 379,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.area'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.area ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.area,\n                                        onChange: (e)=>handleInputChange('area', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"0\",\n                                        min: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 394,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.area\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 402,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 392,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.yearBuilt')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        type: \"number\",\n                                        value: formData.yearBuilt,\n                                        onChange: (e)=>handleInputChange('yearBuilt', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"2024\",\n                                        min: \"1900\",\n                                        max: \"2030\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 409,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 407,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.parking')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 421,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        type: \"number\",\n                                        value: formData.parking,\n                                        onChange: (e)=>handleInputChange('parking', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"0\",\n                                        min: \"0\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 422,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 420,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 406,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 360,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: language === 'ar' ? 'arabic-heading' : '',\n                        children: language === 'ar' ? 'معلومات الموقع' : 'Location Information'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 436,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.location'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.location ? 'error' : ''),\n                                        value: formData.location,\n                                        onChange: (e)=>handleInputChange('location', e.target.value),\n                                        placeholder: t('property.location.placeholder'),\n                                        dir: language === 'ar' ? 'rtl' : 'ltr'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.location\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 450,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 441,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: language === 'ar' ? 'الموقع بالعربية' : 'Location (Arabic)'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 454,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        value: formData.locationAr,\n                                        onChange: (e)=>handleInputChange('locationAr', e.target.value),\n                                        placeholder: \"أدخل موقع العقار بالعربية\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 455,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 453,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 440,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.address'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 467,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.address ? 'error' : ''),\n                                        value: formData.address,\n                                        onChange: (e)=>handleInputChange('address', e.target.value),\n                                        placeholder: t('property.address.placeholder'),\n                                        dir: language === 'ar' ? 'rtl' : 'ltr'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 468,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.address\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 475,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 466,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: language === 'ar' ? 'العنوان بالعربية' : 'Address (Arabic)'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 479,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        value: formData.addressAr,\n                                        onChange: (e)=>handleInputChange('addressAr', e.target.value),\n                                        placeholder: \"أدخل العنوان التفصيلي بالعربية\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 480,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 478,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 465,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.city'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 492,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.city ? 'error' : ''),\n                                        value: formData.city,\n                                        onChange: (e)=>handleInputChange('city', e.target.value),\n                                        placeholder: t('property.city.placeholder'),\n                                        dir: language === 'ar' ? 'rtl' : 'ltr'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 493,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.city && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.city\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 500,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 491,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: language === 'ar' ? 'المدينة بالعربية' : 'City (Arabic)'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 504,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        value: formData.cityAr,\n                                        onChange: (e)=>handleInputChange('cityAr', e.target.value),\n                                        placeholder: \"أدخل اسم المدينة بالعربية\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 505,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 503,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.country')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 515,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.country,\n                                        onValueChange: (value)=>handleInputChange('country', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 518,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 517,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: countries.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: country.value,\n                                                        children: country.label\n                                                    }, country.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 522,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 520,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 514,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 490,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 435,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: language === 'ar' ? 'arabic-heading' : '',\n                        children: language === 'ar' ? 'الوصف' : 'Description'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 534,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.description'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 540,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                        className: \"form-textarea \".concat(errors.description ? 'error' : ''),\n                                        value: formData.description,\n                                        onChange: (e)=>handleInputChange('description', e.target.value),\n                                        placeholder: t('property.description.placeholder'),\n                                        dir: language === 'ar' ? 'rtl' : 'ltr',\n                                        rows: 4\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 549,\n                                        columnNumber: 36\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 539,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: language === 'ar' ? 'الوصف بالعربية' : 'Description (Arabic)'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 553,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                        className: \"form-textarea\",\n                                        value: formData.descriptionAr,\n                                        onChange: (e)=>handleInputChange('descriptionAr', e.target.value),\n                                        placeholder: \"أدخل وصف مفصل للعقار بالعربية\",\n                                        dir: \"rtl\",\n                                        rows: 4\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 554,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 552,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 538,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 533,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: language === 'ar' ? 'arabic-heading' : '',\n                        children: language === 'ar' ? 'خيارات إضافية' : 'Additional Options'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 568,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"furnished\",\n                                        checked: formData.furnished,\n                                        onChange: (e)=>handleInputChange('furnished', e.target.checked),\n                                        className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 574,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"furnished\",\n                                        className: \"form-label\",\n                                        children: t('property.furnished')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 581,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 573,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"petFriendly\",\n                                        checked: formData.petFriendly,\n                                        onChange: (e)=>handleInputChange('petFriendly', e.target.checked),\n                                        className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 587,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"petFriendly\",\n                                        className: \"form-label\",\n                                        children: t('property.petFriendly')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 594,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 586,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 572,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 567,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-4 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"submit\",\n                        disabled: isLoading,\n                        className: \"btn-primary\",\n                        children: [\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading-spinner\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 608,\n                                columnNumber: 25\n                            }, this),\n                            isLoading ? t('properties.loading') : t('properties.save')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 603,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"button\",\n                        onClick: onCancel,\n                        disabled: isLoading,\n                        className: \"btn-secondary\",\n                        children: t('properties.cancel')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 612,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 602,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyCreateForm, \"EwYC4OVtcLowLYjR0+a8zoapcI0=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = PropertyCreateForm;\nvar _c;\n$RefreshReg$(_c, \"PropertyCreateForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/properties/PropertyCreateForm.tsx\n"));

/***/ })

});