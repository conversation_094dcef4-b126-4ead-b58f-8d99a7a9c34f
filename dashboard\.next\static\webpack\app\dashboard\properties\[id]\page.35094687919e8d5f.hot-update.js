"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/[id]/page.tsx":
/*!************************************************!*\
  !*** ./app/dashboard/properties/[id]/page.tsx ***!
  \************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PropertyShowPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _services_propertyService__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/services/propertyService */ \"(app-pages-browser)/./services/propertyService.ts\");\n/* harmony import */ var _components_properties_PropertyShowComponent__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/properties/PropertyShowComponent */ \"(app-pages-browser)/./components/properties/PropertyShowComponent.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Edit,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _styles_arabic_properties_css__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/styles/arabic-properties.css */ \"(app-pages-browser)/./styles/arabic-properties.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction PropertyShowPage() {\n    var _property_images;\n    _s();\n    const params = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams)();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast)();\n    const [property, setProperty] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [isDeleting, setIsDeleting] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const propertyId = params.id;\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertyShowPage.useEffect\": ()=>{\n            if (propertyId) {\n                console.log('Component mounted, property ID:', propertyId);\n                fetchProperty();\n            }\n        }\n    }[\"PropertyShowPage.useEffect\"], [\n        propertyId\n    ]);\n    const fetchProperty = async ()=>{\n        try {\n            setIsLoading(true);\n            console.log('Fetching property:', propertyId);\n            // For now, always use mock data since backend might not be available\n            console.log('Using mock data for property:', propertyId);\n            const mockProperty = {\n                id: propertyId,\n                title: 'فيلا فاخرة في دبي مارينا',\n                titleAr: 'فيلا فاخرة في دبي مارينا',\n                description: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة. تتميز هذه الفيلا بتصميم عصري وموقع استراتيجي في قلب دبي مارينا مع إطلالة بانورامية على البحر والمارينا.',\n                descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة. تتميز هذه الفيلا بتصميم عصري وموقع استراتيجي في قلب دبي مارينا مع إطلالة بانورامية على البحر والمارينا.',\n                price: 2500000,\n                currency: 'AED',\n                type: 'VILLA',\n                status: 'AVAILABLE',\n                bedrooms: 4,\n                bathrooms: 3,\n                area: 350,\n                location: 'دبي مارينا',\n                locationAr: 'دبي مارينا',\n                address: '123 ممشى المارينا',\n                addressAr: '123 ممشى المارينا',\n                city: 'دبي',\n                cityAr: 'دبي',\n                country: 'الإمارات العربية المتحدة',\n                countryAr: 'الإمارات العربية المتحدة',\n                latitude: 25.0772,\n                longitude: 55.1395,\n                images: [\n                    'https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop',\n                    'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop'\n                ],\n                features: [\n                    'مسبح خاص',\n                    'حديقة',\n                    'موقف سيارات',\n                    'أمن 24/7'\n                ],\n                featuresAr: [\n                    'مسبح خاص',\n                    'حديقة',\n                    'موقف سيارات',\n                    'أمن 24/7'\n                ],\n                amenities: [\n                    'صالة رياضية',\n                    'سبا',\n                    'ملعب تنس',\n                    'مارينا خاصة'\n                ],\n                amenitiesAr: [\n                    'صالة رياضية',\n                    'سبا',\n                    'ملعب تنس',\n                    'مارينا خاصة'\n                ],\n                yearBuilt: 2020,\n                parking: 2,\n                furnished: true,\n                petFriendly: false,\n                utilities: 'جميع المرافق متضمنة',\n                utilitiesAr: 'جميع المرافق متضمنة',\n                contactInfo: '+971 50 123 4567',\n                agentId: 'agent1',\n                isActive: true,\n                isFeatured: true,\n                viewCount: 125,\n                createdAt: new Date().toISOString(),\n                updatedAt: new Date().toISOString(),\n                agent: {\n                    id: 'agent1',\n                    name: 'أحمد محمد',\n                    email: '<EMAIL>'\n                }\n            };\n            console.log('Mock property created:', mockProperty);\n            setProperty(mockProperty);\n            // Try to fetch from API in background (optional)\n            try {\n                const data = await _services_propertyService__WEBPACK_IMPORTED_MODULE_4__.propertyService.getPropertyById(propertyId);\n                console.log('API data received:', data);\n                setProperty(data);\n            } catch (apiError) {\n                console.log('API not available, keeping mock data');\n            }\n        } catch (error) {\n            console.error('Error in fetchProperty:', error);\n        } finally{\n            setIsLoading(false);\n        }\n    };\n    const handleEdit = ()=>{\n        router.push(\"/dashboard/properties/\".concat(propertyId, \"/edit\"));\n    };\n    const handleDelete = async ()=>{\n        if (!confirm('هل أنت متأكد من حذف هذا العقار؟')) {\n            return;\n        }\n        try {\n            setIsDeleting(true);\n            await _services_propertyService__WEBPACK_IMPORTED_MODULE_4__.propertyService.deleteProperty(propertyId);\n            toast({\n                title: 'تم حذف العقار',\n                description: 'تم حذف العقار بنجاح'\n            });\n            router.push('/dashboard/properties');\n        } catch (error) {\n            console.error('Error deleting property:', error);\n            toast({\n                title: 'خطأ في حذف العقار',\n                description: 'حدث خطأ أثناء حذف العقار',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsDeleting(false);\n        }\n    };\n    const handleBack = ()=>{\n        router.push('/dashboard/properties');\n    };\n    if (isLoading) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen property-form-dark rtl arabic-text\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"flex items-center justify-center min-h-[400px]\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"loading-spinner\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 198,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                            className: \"mr-3 text-lg\",\n                            children: \"جاري تحميل العقار...\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 199,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 197,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 196,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 195,\n            columnNumber: 7\n        }, this);\n    }\n    if (!property) {\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"min-h-screen property-form-dark rtl arabic-text\",\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"max-w-7xl mx-auto p-6\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"text-center py-12\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"text-2xl font-bold text-red-400 mb-4\",\n                            children: \"العقار غير موجود\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 211,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-400 mb-6\",\n                            children: \"لم يتم العثور على العقار المطلوب\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 212,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                            onClick: handleBack,\n                            className: \"btn-primary\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                    className: \"h-4 w-4 ml-2\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                    lineNumber: 214,\n                                    columnNumber: 15\n                                }, this),\n                                \"العودة إلى قائمة العقارات\"\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 213,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 210,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                lineNumber: 209,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 208,\n            columnNumber: 7\n        }, this);\n    }\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen property-form-dark rtl arabic-text\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-7xl mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"property-header-dark mb-6\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex items-center justify-between\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleBack,\n                                        variant: \"ghost\",\n                                        className: \"text-gray-400 hover:text-white mb-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 235,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"العودة إلى قائمة العقارات\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 230,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"arabic-heading text-3xl font-bold text-white\",\n                                        children: property.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                        className: \"text-gray-400 mt-2\",\n                                        children: [\n                                            property.location,\n                                            \" • \",\n                                            property.city,\n                                            \" • \",\n                                            property.country\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 241,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 229,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleEdit,\n                                        className: \"btn-secondary\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 251,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"تعديل\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 247,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_6__.Button, {\n                                        onClick: handleDelete,\n                                        disabled: isDeleting,\n                                        className: \"bg-red-600 hover:bg-red-700 text-white\",\n                                        children: [\n                                            isDeleting && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"loading-spinner\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 259,\n                                                columnNumber: 32\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Edit_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                className: \"h-4 w-4 ml-2\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                                lineNumber: 260,\n                                                columnNumber: 17\n                                            }, this),\n                                            \"حذف\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                        lineNumber: 254,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                                lineNumber: 246,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                        lineNumber: 228,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 227,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"mb-4 p-4 bg-gray-800 rounded-lg\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                            className: \"text-white mb-2\",\n                            children: \"Debug Info:\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 269,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: [\n                                \"Property ID: \",\n                                propertyId\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 270,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: [\n                                \"Property loaded: \",\n                                property ? 'Yes' : 'No'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 271,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: [\n                                \"Property title: \",\n                                (property === null || property === void 0 ? void 0 : property.title) || 'N/A'\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 272,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-gray-300\",\n                            children: [\n                                \"Images count: \",\n                                (property === null || property === void 0 ? void 0 : (_property_images = property.images) === null || _property_images === void 0 ? void 0 : _property_images.length) || 0\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                            lineNumber: 273,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 268,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_properties_PropertyShowComponent__WEBPACK_IMPORTED_MODULE_5__.PropertyShowComponent, {\n                    property: property\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n                    lineNumber: 277,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n            lineNumber: 225,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\[id]\\\\page.tsx\",\n        lineNumber: 224,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyShowPage, \"iRGGcDe76PMT/k9AlYn3/VeU8tI=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useParams,\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_3__.useSimpleLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_7__.useToast\n    ];\n});\n_c = PropertyShowPage;\nvar _c;\n$RefreshReg$(_c, \"PropertyShowPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/[id]/page.tsx\n"));

/***/ })

});