"use client"

import { useState, useCallback } from 'react'
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { useToast } from '@/hooks/use-toast'
import { propertyService } from '@/services/propertyService'
import { Upload, X, Image as ImageIcon } from 'lucide-react'
import { useUploadThing } from '@/lib/uploadthing'

interface PropertyFormData {
  title: string
  description: string
  price: number | ''
  currency: string
  type: string
  status: string
  bedrooms: number | ''
  bathrooms: number | ''
  area: number | ''
  location: string
  address: string
  city: string
  country: string
  images: string[] // Store URLs instead of File objects
  features: string[]
  amenities: string[]
  yearBuilt: number | ''
  parking: number | ''
  furnished: boolean
  petFriendly: boolean
}

interface PropertyCreateFormProps {
  onSuccess: () => void
  onCancel: () => void
  isLoading: boolean
  setIsLoading: (loading: boolean) => void
}

export function PropertyCreateForm({ onSuccess, onCancel, isLoading, setIsLoading }: PropertyCreateFormProps) {
  const { t } = useSimpleLanguage()
  const { toast } = useToast()

  // UploadThing hook for property images
  const { startUpload, isUploading } = useUploadThing("propertyImageUploader", {
    onClientUploadComplete: (res) => {
      if (res) {
        const newImageUrls = res.map(file => file.url)
        setFormData(prev => ({
          ...prev,
          images: [...prev.images, ...newImageUrls]
        }))
        toast({
          title: t('images.success'),
          description: t('images.success'),
        })
      }
    },
    onUploadError: (error: Error) => {
      toast({
        title: t('images.error'),
        description: t('images.error'),
        variant: 'destructive',
      })
    },
  })

  const [formData, setFormData] = useState<PropertyFormData>({
    title: '',
    description: '',
    price: '',
    currency: 'USD',
    type: '',
    status: 'AVAILABLE',
    bedrooms: '',
    bathrooms: '',
    area: '',
    location: '',
    address: '',
    city: '',
    country: 'UAE',
    images: [],
    features: [],
    amenities: [],
    yearBuilt: '',
    parking: '',
    furnished: false,
    petFriendly: false,
  })

  const [errors, setErrors] = useState<Record<string, string>>({})
  const [selectedFiles, setSelectedFiles] = useState<File[]>([])

  const handleInputChange = useCallback((field: keyof PropertyFormData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }))
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }))
    }
  }, [errors])

  // Handle image file selection
  const handleImageChange = useCallback((files: FileList | null) => {
    if (!files) return

    const validFiles: File[] = []

    Array.from(files).forEach(file => {
      // Validate file type
      if (!file.type.startsWith('image/')) {
        toast({
          title: t('images.error'),
          description: t('images.fileType'),
          variant: 'destructive',
        })
        return
      }

      // Validate file size (8MB max for UploadThing)
      if (file.size > 8 * 1024 * 1024) {
        toast({
          title: t('images.error'),
          description: t('images.fileSize'),
          variant: 'destructive',
        })
        return
      }

      validFiles.push(file)
    })

    if (validFiles.length > 0) {
      setSelectedFiles(prev => [...prev, ...validFiles])
      // Start upload immediately
      startUpload(validFiles)
    }
  }, [toast, t, startUpload])

  // Remove image
  const removeImage = useCallback((index: number) => {
    setFormData(prev => ({
      ...prev,
      images: prev.images.filter((_, i) => i !== index)
    }))
  }, [])

  // Handle drag and drop
  const handleDragOver = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
  }, [])

  const handleDrop = useCallback((e: React.DragEvent) => {
    e.preventDefault()
    e.stopPropagation()
    handleImageChange(e.dataTransfer.files)
  }, [handleImageChange])

  const validateForm = (): boolean => {
    const newErrors: Record<string, string> = {}

    // Required fields validation
    if (!formData.title.trim()) newErrors.title = t('validation.required')
    if (!formData.description.trim()) newErrors.description = t('validation.required')
    if (!formData.price || formData.price <= 0) newErrors.price = t('validation.positive')
    if (!formData.type) newErrors.type = t('validation.required')
    if (!formData.location.trim()) newErrors.location = t('validation.required')
    if (!formData.address.trim()) newErrors.address = t('validation.required')
    if (!formData.city.trim()) newErrors.city = t('validation.required')
    if (!formData.bedrooms || formData.bedrooms < 0) newErrors.bedrooms = t('validation.positive')
    if (!formData.bathrooms || formData.bathrooms < 0) newErrors.bathrooms = t('validation.positive')
    if (!formData.area || formData.area <= 0) newErrors.area = t('validation.positive')

    setErrors(newErrors)
    return Object.keys(newErrors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (!validateForm()) {
      toast({
        title: t('properties.error'),
        description: t('validation.required'),
        variant: 'destructive',
      })
      return
    }

    setIsLoading(true)

    try {
      const propertyData = {
        title: formData.title,
        description: formData.description,
        price: Number(formData.price),
        currency: formData.currency,
        type: formData.type,
        status: formData.status,
        bedrooms: Number(formData.bedrooms),
        bathrooms: Number(formData.bathrooms),
        area: Number(formData.area),
        location: formData.location,
        address: formData.address,
        city: formData.city,
        country: formData.country,
        images: formData.images, // Already uploaded URLs
        features: formData.features,
        amenities: formData.amenities,
        yearBuilt: formData.yearBuilt ? Number(formData.yearBuilt) : undefined,
        parking: formData.parking ? Number(formData.parking) : undefined,
        furnished: formData.furnished,
        petFriendly: formData.petFriendly,
      }

      await propertyService.createProperty(propertyData)

      toast({
        title: t('properties.success'),
        description: t('properties.success'),
      })

      onSuccess()
    } catch (error) {
      console.error('Error creating property:', error)
      toast({
        title: t('properties.error'),
        description: t('properties.error'),
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const propertyTypes = [
    { value: 'APARTMENT', label: t('property.type.apartment') },
    { value: 'VILLA', label: t('property.type.villa') },
    { value: 'TOWNHOUSE', label: t('property.type.townhouse') },
    { value: 'PENTHOUSE', label: t('property.type.penthouse') },
    { value: 'STUDIO', label: t('property.type.studio') },
    { value: 'OFFICE', label: t('property.type.office') },
    { value: 'SHOP', label: t('property.type.shop') },
    { value: 'WAREHOUSE', label: t('property.type.warehouse') },
    { value: 'LAND', label: t('property.type.land') },
    { value: 'BUILDING', label: t('property.type.building') },
  ]

  const propertyStatuses = [
    { value: 'AVAILABLE', label: t('property.status.available') },
    { value: 'SOLD', label: t('property.status.sold') },
    { value: 'RENTED', label: t('property.status.rented') },
    { value: 'PENDING', label: t('property.status.pending') },
  ]

  const countries = [
    { value: 'UAE', label: t('country.uae') },
    { value: 'SAUDI', label: t('country.saudi') },
    { value: 'QATAR', label: t('country.qatar') },
    { value: 'KUWAIT', label: t('country.kuwait') },
    { value: 'BAHRAIN', label: t('country.bahrain') },
    { value: 'OMAN', label: t('country.oman') },
  ]

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {/* Basic Information */}
      <div className="form-section">
        <h3 className="arabic-heading">المعلومات الأساسية</h3>

        <div className="property-grid property-grid-2">
          <div>
            <Label className="form-label">{t('property.title')} *</Label>
            <Input
              className={`form-input ${errors.title ? 'error' : ''}`}
              value={formData.title}
              onChange={(e) => handleInputChange('title', e.target.value)}
              placeholder={t('property.title.placeholder')}
              dir="rtl"
            />
            {errors.title && <div className="form-error">{errors.title}</div>}
          </div>

          <div>
            <Label className="form-label">{t('property.price')} *</Label>
            <Input
              className={`form-input ${errors.price ? 'error' : ''}`}
              type="number"
              value={formData.price}
              onChange={(e) => handleInputChange('price', e.target.value ? Number(e.target.value) : '')}
              placeholder={t('property.price.placeholder')}
              dir="rtl"
            />
            {errors.price && <div className="form-error">{errors.price}</div>}
          </div>
        </div>

        <div className="property-grid property-grid-2">
          <div>
            <Label className="form-label">{t('property.type')} *</Label>
            <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
              <SelectTrigger className={`form-select ${errors.type ? 'error' : ''}`}>
                <SelectValue placeholder={t('property.type.select')} />
              </SelectTrigger>
              <SelectContent>
                {propertyTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.type && <div className="form-error">{errors.type}</div>}
          </div>

          <div>
            <Label className="form-label">{t('property.status')}</Label>
            <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
              <SelectTrigger className="form-select">
                <SelectValue placeholder={t('property.status.select')} />
              </SelectTrigger>
              <SelectContent>
                {propertyStatuses.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    {status.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Property Details */}
      <div className="form-section">
        <h3 className="arabic-heading">تفاصيل العقار</h3>

        <div className="property-grid property-grid-3">
          <div>
            <Label className="form-label">{t('property.bedrooms')} *</Label>
            <Input
              className={`form-input ${errors.bedrooms ? 'error' : ''}`}
              type="number"
              value={formData.bedrooms}
              onChange={(e) => handleInputChange('bedrooms', e.target.value ? Number(e.target.value) : '')}
              placeholder="٠"
              min="0"
              dir="rtl"
            />
            {errors.bedrooms && <div className="form-error">{errors.bedrooms}</div>}
          </div>

          <div>
            <Label className="form-label">{t('property.bathrooms')} *</Label>
            <Input
              className={`form-input ${errors.bathrooms ? 'error' : ''}`}
              type="number"
              value={formData.bathrooms}
              onChange={(e) => handleInputChange('bathrooms', e.target.value ? Number(e.target.value) : '')}
              placeholder="٠"
              min="0"
              dir="rtl"
            />
            {errors.bathrooms && <div className="form-error">{errors.bathrooms}</div>}
          </div>

          <div>
            <Label className="form-label">{t('property.area')} *</Label>
            <Input
              className={`form-input ${errors.area ? 'error' : ''}`}
              type="number"
              value={formData.area}
              onChange={(e) => handleInputChange('area', e.target.value ? Number(e.target.value) : '')}
              placeholder="٠"
              min="0"
              dir="rtl"
            />
            {errors.area && <div className="form-error">{errors.area}</div>}
          </div>
        </div>

        <div className="property-grid property-grid-2">
          <div>
            <Label className="form-label">{t('property.yearBuilt')}</Label>
            <Input
              className="form-input"
              type="number"
              value={formData.yearBuilt}
              onChange={(e) => handleInputChange('yearBuilt', e.target.value ? Number(e.target.value) : '')}
              placeholder="٢٠٢٤"
              min="1900"
              max="2030"
              dir="rtl"
            />
          </div>

          <div>
            <Label className="form-label">{t('property.parking')}</Label>
            <Input
              className="form-input"
              type="number"
              value={formData.parking}
              onChange={(e) => handleInputChange('parking', e.target.value ? Number(e.target.value) : '')}
              placeholder="٠"
              min="0"
              dir="rtl"
            />
          </div>
        </div>
      </div>

      {/* Location Information */}
      <div className="form-section">
        <h3 className="arabic-heading">معلومات الموقع</h3>

        <div className="property-grid property-grid-2">
          <div>
            <Label className="form-label">{t('property.location')} *</Label>
            <Input
              className={`form-input ${errors.location ? 'error' : ''}`}
              value={formData.location}
              onChange={(e) => handleInputChange('location', e.target.value)}
              placeholder={t('property.location.placeholder')}
              dir="rtl"
            />
            {errors.location && <div className="form-error">{errors.location}</div>}
          </div>

          <div>
            <Label className="form-label">{t('property.address')} *</Label>
            <Input
              className={`form-input ${errors.address ? 'error' : ''}`}
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              placeholder={t('property.address.placeholder')}
              dir="rtl"
            />
            {errors.address && <div className="form-error">{errors.address}</div>}
          </div>
        </div>

        <div className="property-grid property-grid-2">
          <div>
            <Label className="form-label">{t('property.city')} *</Label>
            <Input
              className={`form-input ${errors.city ? 'error' : ''}`}
              value={formData.city}
              onChange={(e) => handleInputChange('city', e.target.value)}
              placeholder={t('property.city.placeholder')}
              dir="rtl"
            />
            {errors.city && <div className="form-error">{errors.city}</div>}
          </div>

          <div>
            <Label className="form-label">{t('property.country')}</Label>
            <Select value={formData.country} onValueChange={(value) => handleInputChange('country', value)}>
              <SelectTrigger className="form-select">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {countries.map((country) => (
                  <SelectItem key={country.value} value={country.value}>
                    {country.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </div>
      </div>

      {/* Description */}
      <div className="form-section">
        <h3 className="arabic-heading">الوصف</h3>

        <div>
          <Label className="form-label">{t('property.description')} *</Label>
          <Textarea
            className={`form-textarea ${errors.description ? 'error' : ''}`}
            value={formData.description}
            onChange={(e) => handleInputChange('description', e.target.value)}
            placeholder={t('property.description.placeholder')}
            dir="rtl"
            rows={4}
          />
          {errors.description && <div className="form-error">{errors.description}</div>}
        </div>
      </div>

      {/* Image Upload */}
      <div className="form-section">
        <h3 className="arabic-heading">{t('property.images')}</h3>

        <div className="space-y-4">
          {/* Upload Area */}
          <div
            className={`image-upload-area ${isUploading ? 'loading' : ''}`}
            onDragOver={handleDragOver}
            onDrop={handleDrop}
            onClick={() => {
              const input = document.createElement('input')
              input.type = 'file'
              input.multiple = true
              input.accept = 'image/*'
              input.onchange = (e) => {
                const target = e.target as HTMLInputElement
                handleImageChange(target.files)
              }
              input.click()
            }}
          >
            <div className="flex flex-col items-center justify-center py-8">
              <Upload className="h-12 w-12 text-gray-400 mb-4" />
              <p className="text-lg font-medium text-gray-300 mb-2">
                {t('images.drag')}
              </p>
              <p className="text-sm text-gray-500">
                {t('images.formats')}
              </p>
              {isUploading && (
                <div className="mt-4 flex items-center">
                  <div className="loading-spinner" />
                  <span className="mr-2">{t('images.uploading')}</span>
                </div>
              )}
            </div>
          </div>

          {/* Image Previews */}
          {formData.images.length > 0 && (
            <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {formData.images.map((url, index) => (
                <div key={index} className="relative group">
                  <div className="aspect-square bg-gray-800 rounded-lg overflow-hidden">
                    <img
                      src={url}
                      alt={`${t('images.preview')} ${index + 1}`}
                      className="w-full h-full object-cover"
                    />
                  </div>
                  <button
                    type="button"
                    onClick={() => removeImage(index)}
                    className="absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity"
                    title={t('images.remove')}
                  >
                    <X className="h-4 w-4" />
                  </button>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>

      {/* Additional Options */}
      <div className="form-section">
        <h3 className="arabic-heading">خيارات إضافية</h3>

        <div className="property-grid property-grid-2">
          <div className="flex items-center space-x-2 space-x-reverse">
            <input
              type="checkbox"
              id="furnished"
              checked={formData.furnished}
              onChange={(e) => handleInputChange('furnished', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500"
            />
            <Label htmlFor="furnished" className="form-label">
              {t('property.furnished')}
            </Label>
          </div>

          <div className="flex items-center space-x-2 space-x-reverse">
            <input
              type="checkbox"
              id="petFriendly"
              checked={formData.petFriendly}
              onChange={(e) => handleInputChange('petFriendly', e.target.checked)}
              className="w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500"
            />
            <Label htmlFor="petFriendly" className="form-label">
              {t('property.petFriendly')}
            </Label>
          </div>
        </div>
      </div>

      {/* Action Buttons */}
      <div className="flex gap-4 flex-row-reverse">
        <Button
          type="submit"
          disabled={isLoading || isUploading}
          className="btn-primary"
        >
          {(isLoading || isUploading) && <div className="loading-spinner" />}
          {(isLoading || isUploading) ? t('properties.loading') : t('properties.save')}
        </Button>

        <Button
          type="button"
          onClick={onCancel}
          disabled={isLoading || isUploading}
          className="btn-secondary"
        >
          {t('properties.cancel')}
        </Button>
      </div>
    </form>
  )
}
