/**
 * Utility function to format price with proper currency handling
 * Handles invalid currencies and provides fallback formatting
 */

export function formatPrice(price: number, currency: string, locale: string = 'ar-AE'): string {
  // Ensure currency is valid, default to USD if not provided
  const validCurrency = currency && currency.length === 3 ? currency.toUpperCase() : 'USD';
  
  // Validate price
  if (typeof price !== 'number' || isNaN(price)) {
    return '0 ' + validCurrency;
  }
  
  try {
    return new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: validCurrency,
      minimumFractionDigits: 0,
      maximumFractionDigits: 0,
    }).format(price);
  } catch (error) {
    // Fallback to simple number formatting if currency is invalid
    try {
      return new Intl.NumberFormat(locale, {
        minimumFractionDigits: 0,
        maximumFractionDigits: 0,
      }).format(price) + ' ' + validCurrency;
    } catch (fallbackError) {
      // Ultimate fallback
      return price.toLocaleString() + ' ' + validCurrency;
    }
  }
}

/**
 * Format price specifically for Arabic locale
 */
export function formatPriceArabic(price: number, currency: string): string {
  return formatPrice(price, currency, 'ar-AE');
}

/**
 * Format price specifically for English locale
 */
export function formatPriceEnglish(price: number, currency: string): string {
  return formatPrice(price, currency, 'en-US');
}

/**
 * Get supported currencies list
 */
export const SUPPORTED_CURRENCIES = [
  'USD', 'EUR', 'GBP', 'AED', 'SAR', 'QAR', 'KWD', 'BHD', 'OMR'
] as const;

/**
 * Validate if currency code is supported
 */
export function isSupportedCurrency(currency: string): boolean {
  return SUPPORTED_CURRENCIES.includes(currency as any);
}

/**
 * Get currency symbol for a given currency code
 */
export function getCurrencySymbol(currency: string, locale: string = 'ar-AE'): string {
  const validCurrency = currency && currency.length === 3 ? currency.toUpperCase() : 'USD';
  
  try {
    const formatter = new Intl.NumberFormat(locale, {
      style: 'currency',
      currency: validCurrency,
      minimumFractionDigits: 0,
    });
    
    // Extract just the currency symbol
    const parts = formatter.formatToParts(0);
    const currencyPart = parts.find(part => part.type === 'currency');
    return currencyPart?.value || validCurrency;
  } catch (error) {
    return validCurrency;
  }
}
