'use client';

import { useState, useEffect } from 'react';
import { cleanupCookies, safeLog } from '@/lib/cookieCleanup';
import { resources } from '@/lib/i18n/settings';

/**
 * Arabic-only language hook for Properties system
 * Supports Arabic language with RTL text direction
 */
export function useSimpleLanguage() {
  const [language] = useState<'ar'>('ar'); // Arabic only

  // Initialize Arabic interface with cleanup
  useEffect(() => {
    // Clean up cookies first
    cleanupCookies();

    // Set Arabic language preference
    localStorage.setItem('properties-language', 'ar');

    safeLog('🏠 Arabic Properties system initialized');
  }, []);

  // Set Arabic document properties
  useEffect(() => {
    // Update document properties for Arabic
    document.documentElement.lang = 'ar';
    document.documentElement.dir = 'rtl';

    // Update CSS classes for Arabic
    document.documentElement.className = 'rtl arabic-interface';

    // Set Arabic fonts
    document.body.style.fontFamily = "'Cairo', 'Noto Sans Arabic', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', sans-serif";

    safeLog('🌐 Arabic language interface active');
  }, []);

  // Translation function for Arabic with direct mapping
  const translations = {
    'properties.create': 'إنشاء عقار جديد',
    'properties.subtitle': 'إدارة وإضافة العقارات الجديدة',
    'properties.save': 'حفظ العقار',
    'properties.cancel': 'إلغاء',
    'properties.loading': 'جاري التحميل...',
    'properties.success': 'تم حفظ العقار بنجاح',
    'properties.error': 'حدث خطأ أثناء حفظ العقار',

    'property.title': 'عنوان العقار',
    'property.title.placeholder': 'أدخل عنوان العقار',
    'property.description': 'وصف العقار',
    'property.description.placeholder': 'أدخل وصف مفصل للعقار',
    'property.price': 'السعر',
    'property.price.placeholder': 'أدخل سعر العقار',
    'property.type': 'نوع العقار',
    'property.type.select': 'اختر نوع العقار',
    'property.status': 'حالة العقار',
    'property.status.select': 'اختر حالة العقار',
    'property.bedrooms': 'عدد غرف النوم',
    'property.bathrooms': 'عدد دورات المياه',
    'property.area': 'المساحة',
    'property.location': 'الموقع',
    'property.location.placeholder': 'أدخل موقع العقار',
    'property.address': 'العنوان',
    'property.address.placeholder': 'أدخل العنوان التفصيلي',
    'property.city': 'المدينة',
    'property.city.placeholder': 'أدخل اسم المدينة',
    'property.country': 'الدولة',
    'property.images': 'صور العقار',
    'property.yearBuilt': 'سنة البناء',
    'property.parking': 'مواقف السيارات',
    'property.furnished': 'مفروش',
    'property.petFriendly': 'يسمح بالحيوانات الأليفة',

    'property.type.apartment': 'شقة',
    'property.type.villa': 'فيلا',
    'property.type.townhouse': 'تاون هاوس',
    'property.type.penthouse': 'بنتهاوس',
    'property.type.studio': 'استوديو',
    'property.type.office': 'مكتب',
    'property.type.shop': 'محل تجاري',
    'property.type.warehouse': 'مستودع',
    'property.type.land': 'أرض',
    'property.type.building': 'مبنى',

    'property.status.available': 'متاح',
    'property.status.sold': 'مباع',
    'property.status.rented': 'مؤجر',
    'property.status.pending': 'قيد المراجعة',

    'country.uae': 'الإمارات العربية المتحدة',
    'country.saudi': 'المملكة العربية السعودية',
    'country.qatar': 'قطر',
    'country.kuwait': 'الكويت',
    'country.bahrain': 'البحرين',
    'country.oman': 'عمان',

    'images.drag': 'اسحب الصور هنا أو انقر للاختيار',
    'images.formats': 'صور حتى ٨ ميجابايت',
    'images.uploading': 'جاري رفع الصور...',
    'images.success': 'تم رفع الصور بنجاح',
    'images.error': 'خطأ في رفع الصور',
    'images.remove': 'حذف الصورة',
    'images.preview': 'معاينة الصورة',
    'images.fileType': 'يرجى اختيار ملفات صور فقط',
    'images.fileSize': 'حجم الصورة يجب أن يكون أقل من ٨ ميجابايت',

    'validation.required': 'هذا الحقل مطلوب',
    'validation.positive': 'يجب أن يكون الرقم أكبر من الصفر',
  };

  const t = (key: string): string => {
    return translations[key as keyof typeof translations] || key;
  };

  return {
    language,
    setLanguage: () => {}, // No language switching needed
    isRTL: true,
    isArabic: true,
    isEnglish: false,
    t,
  };
}
