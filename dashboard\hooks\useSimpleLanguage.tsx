'use client';

import { useState, useEffect } from 'react';
import { cleanupCookies, safeLog } from '@/lib/cookieCleanup';
import { resources } from '@/lib/i18n/settings';

/**
 * Arabic-only language hook for Properties system
 * Supports Arabic language with RTL text direction
 */
export function useSimpleLanguage() {
  const [language] = useState<'ar'>('ar'); // Arabic only

  // Initialize Arabic interface with cleanup
  useEffect(() => {
    // Clean up cookies first
    cleanupCookies();

    // Set Arabic language preference
    localStorage.setItem('properties-language', 'ar');

    safeLog('🏠 Arabic Properties system initialized');
  }, []);

  // Set Arabic document properties
  useEffect(() => {
    // Update document properties for Arabic
    document.documentElement.lang = 'ar';
    document.documentElement.dir = 'rtl';

    // Update CSS classes for Arabic
    document.documentElement.className = 'rtl arabic-interface';

    // Set Arabic fonts
    document.body.style.fontFamily = "'Cairo', 'Noto Sans Arabic', '<PERSON><PERSON><PERSON>', '<PERSON><PERSON>', sans-serif";

    safeLog('🌐 Arabic language interface active');
  }, []);

  // Translation function for Arabic
  const t = (key: string): string => {
    try {
      const keys = key.split('.');
      let value: any = resources.ar?.translation;

      for (const k of keys) {
        value = value?.[k];
      }

      return value || key;
    } catch (error) {
      return key;
    }
  };

  return {
    language,
    setLanguage: () => {}, // No language switching needed
    isRTL: true,
    isArabic: true,
    isEnglish: false,
    t,
  };
}
