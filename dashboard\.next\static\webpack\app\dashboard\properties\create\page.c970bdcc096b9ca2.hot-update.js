"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./components/properties/PropertyCreateForm.tsx":
/*!******************************************************!*\
  !*** ./components/properties/PropertyCreateForm.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyCreateForm: () => (/* binding */ PropertyCreateForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _services_propertyService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/propertyService */ \"(app-pages-browser)/./services/propertyService.ts\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/upload.js\");\n/* harmony import */ var _barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Upload,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* __next_internal_client_entry_do_not_use__ PropertyCreateForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction PropertyCreateForm(param) {\n    let { onSuccess, onCancel, isLoading, setIsLoading } = param;\n    _s();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const fileInputRef = useRef(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        price: '',\n        currency: 'USD',\n        type: '',\n        status: 'AVAILABLE',\n        bedrooms: '',\n        bathrooms: '',\n        area: '',\n        location: '',\n        address: '',\n        city: '',\n        country: 'UAE',\n        images: [],\n        features: [],\n        amenities: [],\n        yearBuilt: '',\n        parking: '',\n        furnished: false,\n        petFriendly: false\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [imagePreviewUrls, setImagePreviewUrls] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleInputChange]\": (field, value)=>{\n            setFormData({\n                \"PropertyCreateForm.useCallback[handleInputChange]\": (prev)=>({\n                        ...prev,\n                        [field]: value\n                    })\n            }[\"PropertyCreateForm.useCallback[handleInputChange]\"]);\n            // Clear error when user starts typing\n            if (errors[field]) {\n                setErrors({\n                    \"PropertyCreateForm.useCallback[handleInputChange]\": (prev)=>({\n                            ...prev,\n                            [field]: ''\n                        })\n                }[\"PropertyCreateForm.useCallback[handleInputChange]\"]);\n            }\n        }\n    }[\"PropertyCreateForm.useCallback[handleInputChange]\"], [\n        errors\n    ]);\n    // Handle image file selection\n    const handleImageChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleImageChange]\": (files)=>{\n            if (!files) return;\n            const validFiles = [];\n            const newPreviewUrls = [];\n            Array.from(files).forEach({\n                \"PropertyCreateForm.useCallback[handleImageChange]\": (file)=>{\n                    // Validate file type\n                    if (!file.type.startsWith('image/')) {\n                        toast({\n                            title: t('images.error'),\n                            description: 'يرجى اختيار ملفات صور فقط',\n                            variant: 'destructive'\n                        });\n                        return;\n                    }\n                    // Validate file size (10MB max)\n                    if (file.size > 10 * 1024 * 1024) {\n                        toast({\n                            title: t('images.error'),\n                            description: 'حجم الصورة يجب أن يكون أقل من 10MB',\n                            variant: 'destructive'\n                        });\n                        return;\n                    }\n                    validFiles.push(file);\n                    newPreviewUrls.push(URL.createObjectURL(file));\n                }\n            }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n            if (validFiles.length > 0) {\n                setFormData({\n                    \"PropertyCreateForm.useCallback[handleImageChange]\": (prev)=>({\n                            ...prev,\n                            images: [\n                                ...prev.images,\n                                ...validFiles\n                            ]\n                        })\n                }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n                setImagePreviewUrls({\n                    \"PropertyCreateForm.useCallback[handleImageChange]\": (prev)=>[\n                            ...prev,\n                            ...newPreviewUrls\n                        ]\n                }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n            }\n        }\n    }[\"PropertyCreateForm.useCallback[handleImageChange]\"], [\n        toast,\n        t\n    ]);\n    // Remove image\n    const removeImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[removeImage]\": (index)=>{\n            setFormData({\n                \"PropertyCreateForm.useCallback[removeImage]\": (prev)=>({\n                        ...prev,\n                        images: prev.images.filter({\n                            \"PropertyCreateForm.useCallback[removeImage]\": (_, i)=>i !== index\n                        }[\"PropertyCreateForm.useCallback[removeImage]\"])\n                    })\n            }[\"PropertyCreateForm.useCallback[removeImage]\"]);\n            // Revoke URL to prevent memory leaks\n            URL.revokeObjectURL(imagePreviewUrls[index]);\n            setImagePreviewUrls({\n                \"PropertyCreateForm.useCallback[removeImage]\": (prev)=>prev.filter({\n                        \"PropertyCreateForm.useCallback[removeImage]\": (_, i)=>i !== index\n                    }[\"PropertyCreateForm.useCallback[removeImage]\"])\n            }[\"PropertyCreateForm.useCallback[removeImage]\"]);\n        }\n    }[\"PropertyCreateForm.useCallback[removeImage]\"], [\n        imagePreviewUrls\n    ]);\n    // Handle drag and drop\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n        }\n    }[\"PropertyCreateForm.useCallback[handleDragOver]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            handleImageChange(e.dataTransfer.files);\n        }\n    }[\"PropertyCreateForm.useCallback[handleDrop]\"], [\n        handleImageChange\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Required fields validation\n        if (!formData.title.trim()) newErrors.title = t('validation.required');\n        if (!formData.description.trim()) newErrors.description = t('validation.required');\n        if (!formData.price || formData.price <= 0) newErrors.price = t('validation.positive');\n        if (!formData.type) newErrors.type = t('validation.required');\n        if (!formData.location.trim()) newErrors.location = t('validation.required');\n        if (!formData.address.trim()) newErrors.address = t('validation.required');\n        if (!formData.city.trim()) newErrors.city = t('validation.required');\n        if (!formData.bedrooms || formData.bedrooms < 0) newErrors.bedrooms = t('validation.positive');\n        if (!formData.bathrooms || formData.bathrooms < 0) newErrors.bathrooms = t('validation.positive');\n        if (!formData.area || formData.area <= 0) newErrors.area = t('validation.positive');\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            toast({\n                title: t('properties.error'),\n                description: t('validation.required'),\n                variant: 'destructive'\n            });\n            return;\n        }\n        setIsLoading(true);\n        setUploadingImages(true);\n        try {\n            // Upload images first if any\n            let imageUrls = [];\n            if (formData.images.length > 0) {\n                toast({\n                    title: t('images.uploading'),\n                    description: 'جاري رفع الصور...'\n                });\n                // Create FormData for image upload\n                const imageFormData = new FormData();\n                formData.images.forEach((file, index)=>{\n                    imageFormData.append(\"images\", file);\n                });\n                // Here you would typically upload to your image service\n                // For now, we'll create mock URLs\n                imageUrls = formData.images.map((file, index)=>\"https://example.com/images/\".concat(Date.now(), \"-\").concat(index, \"-\").concat(file.name));\n            }\n            const propertyData = {\n                title: formData.title,\n                description: formData.description,\n                price: Number(formData.price),\n                currency: formData.currency,\n                type: formData.type,\n                status: formData.status,\n                bedrooms: Number(formData.bedrooms),\n                bathrooms: Number(formData.bathrooms),\n                area: Number(formData.area),\n                location: formData.location,\n                address: formData.address,\n                city: formData.city,\n                country: formData.country,\n                images: imageUrls,\n                features: formData.features,\n                amenities: formData.amenities,\n                yearBuilt: formData.yearBuilt ? Number(formData.yearBuilt) : undefined,\n                parking: formData.parking ? Number(formData.parking) : undefined,\n                furnished: formData.furnished,\n                petFriendly: formData.petFriendly\n            };\n            await _services_propertyService__WEBPACK_IMPORTED_MODULE_9__.propertyService.createProperty(propertyData);\n            toast({\n                title: t('properties.success'),\n                description: 'تم إنشاء العقار بنجاح'\n            });\n            onSuccess();\n        } catch (error) {\n            console.error('Error creating property:', error);\n            toast({\n                title: t('properties.error'),\n                description: 'حدث خطأ أثناء إنشاء العقار',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsLoading(false);\n            setUploadingImages(false);\n        }\n    };\n    const propertyTypes = [\n        {\n            value: 'APARTMENT',\n            label: t('property.type.apartment')\n        },\n        {\n            value: 'VILLA',\n            label: t('property.type.villa')\n        },\n        {\n            value: 'TOWNHOUSE',\n            label: t('property.type.townhouse')\n        },\n        {\n            value: 'PENTHOUSE',\n            label: t('property.type.penthouse')\n        },\n        {\n            value: 'STUDIO',\n            label: t('property.type.studio')\n        },\n        {\n            value: 'OFFICE',\n            label: t('property.type.office')\n        },\n        {\n            value: 'SHOP',\n            label: t('property.type.shop')\n        },\n        {\n            value: 'WAREHOUSE',\n            label: t('property.type.warehouse')\n        },\n        {\n            value: 'LAND',\n            label: t('property.type.land')\n        },\n        {\n            value: 'BUILDING',\n            label: t('property.type.building')\n        }\n    ];\n    const propertyStatuses = [\n        {\n            value: 'AVAILABLE',\n            label: t('property.status.available')\n        },\n        {\n            value: 'SOLD',\n            label: t('property.status.sold')\n        },\n        {\n            value: 'RENTED',\n            label: t('property.status.rented')\n        },\n        {\n            value: 'PENDING',\n            label: t('property.status.pending')\n        }\n    ];\n    const countries = [\n        {\n            value: 'UAE',\n            label: t('country.uae')\n        },\n        {\n            value: 'SAUDI',\n            label: t('country.saudi')\n        },\n        {\n            value: 'QATAR',\n            label: t('country.qatar')\n        },\n        {\n            value: 'KUWAIT',\n            label: t('country.kuwait')\n        },\n        {\n            value: 'BAHRAIN',\n            label: t('country.bahrain')\n        },\n        {\n            value: 'OMAN',\n            label: t('country.oman')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"المعلومات الأساسية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 283,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.title'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.title ? 'error' : ''),\n                                        value: formData.title,\n                                        onChange: (e)=>handleInputChange('title', e.target.value),\n                                        placeholder: t('property.title.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 288,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 295,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 286,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.price'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.price ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.price,\n                                        onChange: (e)=>handleInputChange('price', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: t('property.price.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 300,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.price\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 308,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 285,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.type'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.type,\n                                        onValueChange: (value)=>handleInputChange('type', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select \".concat(errors.type ? 'error' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: \"اختر نوع العقار\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 317,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 316,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: propertyTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: type.value,\n                                                        children: type.label\n                                                    }, type.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 321,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 319,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 315,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.type\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 327,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.status')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.status,\n                                        onValueChange: (value)=>handleInputChange('status', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 334,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 333,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: propertyStatuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: status.value,\n                                                        children: status.label\n                                                    }, status.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 338,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 336,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 332,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 330,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 282,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"تفاصيل العقار\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 350,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.bedrooms'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.bedrooms ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.bedrooms,\n                                        onChange: (e)=>handleInputChange('bedrooms', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"0\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.bedrooms\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.bathrooms'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.bathrooms ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.bathrooms,\n                                        onChange: (e)=>handleInputChange('bathrooms', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"0\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 369,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.bathrooms\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 378,\n                                        columnNumber: 34\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 367,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.area'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.area ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.area,\n                                        onChange: (e)=>handleInputChange('area', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"0\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.area\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 392,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 352,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.yearBuilt')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        type: \"number\",\n                                        value: formData.yearBuilt,\n                                        onChange: (e)=>handleInputChange('yearBuilt', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"2024\",\n                                        min: \"1900\",\n                                        max: \"2030\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 399,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 397,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.parking')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        type: \"number\",\n                                        value: formData.parking,\n                                        onChange: (e)=>handleInputChange('parking', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"0\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 413,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 411,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 396,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 349,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"معلومات الموقع\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 428,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.location'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.location ? 'error' : ''),\n                                        value: formData.location,\n                                        onChange: (e)=>handleInputChange('location', e.target.value),\n                                        placeholder: t('property.location.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 433,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.location\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 440,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 431,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.address'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.address ? 'error' : ''),\n                                        value: formData.address,\n                                        onChange: (e)=>handleInputChange('address', e.target.value),\n                                        placeholder: t('property.address.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 445,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.address\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 452,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 443,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 430,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.city'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.city ? 'error' : ''),\n                                        value: formData.city,\n                                        onChange: (e)=>handleInputChange('city', e.target.value),\n                                        placeholder: t('property.city.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 459,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.city && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.city\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 457,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.country')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.country,\n                                        onValueChange: (value)=>handleInputChange('country', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 473,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 472,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: countries.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: country.value,\n                                                        children: country.label\n                                                    }, country.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 477,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 475,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 471,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 469,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 456,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 427,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"الوصف\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 489,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                className: \"form-label\",\n                                children: [\n                                    t('property.description'),\n                                    \" *\"\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 492,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                className: \"form-textarea \".concat(errors.description ? 'error' : ''),\n                                value: formData.description,\n                                onChange: (e)=>handleInputChange('description', e.target.value),\n                                placeholder: t('property.description.placeholder'),\n                                dir: \"rtl\",\n                                rows: 4\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 11\n                            }, this),\n                            errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"form-error\",\n                                children: errors.description\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 501,\n                                columnNumber: 34\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 491,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 488,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: t('property.images')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 507,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-4\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"image-upload-area \".concat(uploadingImages ? 'loading' : ''),\n                                onDragOver: handleDragOver,\n                                onDrop: handleDrop,\n                                onClick: ()=>{\n                                    var _fileInputRef_current;\n                                    return (_fileInputRef_current = fileInputRef.current) === null || _fileInputRef_current === void 0 ? void 0 : _fileInputRef_current.click();\n                                },\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"flex flex-col items-center justify-center py-8\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                            className: \"h-12 w-12 text-gray-400 mb-4\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 518,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-lg font-medium text-gray-300 mb-2\",\n                                            children: t('images.drag')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 519,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-sm text-gray-500\",\n                                            children: t('images.formats')\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 522,\n                                            columnNumber: 15\n                                        }, this),\n                                        uploadingImages && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"mt-4 flex items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"loading-spinner\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 527,\n                                                    columnNumber: 19\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"mr-2\",\n                                                    children: t('images.uploading')\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 528,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                            lineNumber: 526,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                    lineNumber: 517,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 511,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                ref: fileInputRef,\n                                type: \"file\",\n                                multiple: true,\n                                accept: \"image/*\",\n                                onChange: (e)=>handleImageChange(e.target.files),\n                                className: \"hidden\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 535,\n                                columnNumber: 11\n                            }, this),\n                            imagePreviewUrls.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4\",\n                                children: imagePreviewUrls.map((url, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative group\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"aspect-square bg-gray-800 rounded-lg overflow-hidden\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                    src: url,\n                                                    alt: \"\".concat(t('images.preview'), \" \").concat(index + 1),\n                                                    className: \"w-full h-full object-cover\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 550,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 549,\n                                                columnNumber: 19\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                                type: \"button\",\n                                                onClick: ()=>removeImage(index),\n                                                className: \"absolute top-2 right-2 bg-red-600 hover:bg-red-700 text-white rounded-full p-1 opacity-0 group-hover:opacity-100 transition-opacity\",\n                                                title: t('images.remove'),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Upload_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                    className: \"h-4 w-4\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 562,\n                                                    columnNumber: 21\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 556,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, index, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 17\n                                    }, this))\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 546,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 509,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 506,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"خيارات إضافية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 573,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"furnished\",\n                                        checked: formData.furnished,\n                                        onChange: (e)=>handleInputChange('furnished', e.target.checked),\n                                        className: \"w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 577,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"furnished\",\n                                        className: \"form-label\",\n                                        children: t('property.furnished')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 584,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 576,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2 space-x-reverse\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"petFriendly\",\n                                        checked: formData.petFriendly,\n                                        onChange: (e)=>handleInputChange('petFriendly', e.target.checked),\n                                        className: \"w-4 h-4 text-blue-600 bg-gray-800 border-gray-600 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 590,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"petFriendly\",\n                                        className: \"form-label\",\n                                        children: t('property.petFriendly')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 597,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 589,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 575,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 572,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-4 flex-row-reverse\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"submit\",\n                        disabled: isLoading || uploadingImages,\n                        className: \"btn-primary\",\n                        children: [\n                            (isLoading || uploadingImages) && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading-spinner\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 611,\n                                columnNumber: 46\n                            }, this),\n                            isLoading || uploadingImages ? t('properties.loading') : t('properties.save')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 606,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"button\",\n                        onClick: onCancel,\n                        disabled: isLoading || uploadingImages,\n                        className: \"btn-secondary\",\n                        children: t('properties.cancel')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 615,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 605,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n        lineNumber: 280,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyCreateForm, \"EwYC4OVtcLowLYjR0+a8zoapcI0=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = PropertyCreateForm;\nvar _c;\n$RefreshReg$(_c, \"PropertyCreateForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/properties/PropertyCreateForm.tsx\n"));

/***/ })

});