"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./components/properties/PropertyCreateForm.tsx":
/*!******************************************************!*\
  !*** ./components/properties/PropertyCreateForm.tsx ***!
  \******************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyCreateForm: () => (/* binding */ PropertyCreateForm)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_label__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/label */ \"(app-pages-browser)/./components/ui/label.tsx\");\n/* harmony import */ var _components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/textarea */ \"(app-pages-browser)/./components/ui/textarea.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/use-toast */ \"(app-pages-browser)/./hooks/use-toast.ts\");\n/* harmony import */ var _services_propertyService__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/services/propertyService */ \"(app-pages-browser)/./services/propertyService.ts\");\n/* __next_internal_client_entry_do_not_use__ PropertyCreateForm auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction PropertyCreateForm(param) {\n    let { onSuccess, onCancel, isLoading, setIsLoading } = param;\n    _s();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage)();\n    const { toast } = (0,_hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast)();\n    const fileInputRef = (0,react__WEBPACK_IMPORTED_MODULE_1__.useRef)(null);\n    const [formData, setFormData] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({\n        title: '',\n        description: '',\n        price: '',\n        currency: 'USD',\n        type: '',\n        status: 'AVAILABLE',\n        bedrooms: '',\n        bathrooms: '',\n        area: '',\n        location: '',\n        address: '',\n        city: '',\n        country: 'UAE',\n        images: [],\n        features: [],\n        amenities: [],\n        yearBuilt: '',\n        parking: '',\n        furnished: false,\n        petFriendly: false\n    });\n    const [errors, setErrors] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)({});\n    const [imagePreviewUrls, setImagePreviewUrls] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [uploadingImages, setUploadingImages] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleInputChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleInputChange]\": (field, value)=>{\n            setFormData({\n                \"PropertyCreateForm.useCallback[handleInputChange]\": (prev)=>({\n                        ...prev,\n                        [field]: value\n                    })\n            }[\"PropertyCreateForm.useCallback[handleInputChange]\"]);\n            // Clear error when user starts typing\n            if (errors[field]) {\n                setErrors({\n                    \"PropertyCreateForm.useCallback[handleInputChange]\": (prev)=>({\n                            ...prev,\n                            [field]: ''\n                        })\n                }[\"PropertyCreateForm.useCallback[handleInputChange]\"]);\n            }\n        }\n    }[\"PropertyCreateForm.useCallback[handleInputChange]\"], [\n        errors\n    ]);\n    // Handle image file selection\n    const handleImageChange = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleImageChange]\": (files)=>{\n            if (!files) return;\n            const validFiles = [];\n            const newPreviewUrls = [];\n            Array.from(files).forEach({\n                \"PropertyCreateForm.useCallback[handleImageChange]\": (file)=>{\n                    // Validate file type\n                    if (!file.type.startsWith('image/')) {\n                        toast({\n                            title: t('images.error'),\n                            description: 'يرجى اختيار ملفات صور فقط',\n                            variant: 'destructive'\n                        });\n                        return;\n                    }\n                    // Validate file size (10MB max)\n                    if (file.size > 10 * 1024 * 1024) {\n                        toast({\n                            title: t('images.error'),\n                            description: 'حجم الصورة يجب أن يكون أقل من 10MB',\n                            variant: 'destructive'\n                        });\n                        return;\n                    }\n                    validFiles.push(file);\n                    newPreviewUrls.push(URL.createObjectURL(file));\n                }\n            }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n            if (validFiles.length > 0) {\n                setFormData({\n                    \"PropertyCreateForm.useCallback[handleImageChange]\": (prev)=>({\n                            ...prev,\n                            images: [\n                                ...prev.images,\n                                ...validFiles\n                            ]\n                        })\n                }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n                setImagePreviewUrls({\n                    \"PropertyCreateForm.useCallback[handleImageChange]\": (prev)=>[\n                            ...prev,\n                            ...newPreviewUrls\n                        ]\n                }[\"PropertyCreateForm.useCallback[handleImageChange]\"]);\n            }\n        }\n    }[\"PropertyCreateForm.useCallback[handleImageChange]\"], [\n        toast,\n        t\n    ]);\n    // Remove image\n    const removeImage = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[removeImage]\": (index)=>{\n            setFormData({\n                \"PropertyCreateForm.useCallback[removeImage]\": (prev)=>({\n                        ...prev,\n                        images: prev.images.filter({\n                            \"PropertyCreateForm.useCallback[removeImage]\": (_, i)=>i !== index\n                        }[\"PropertyCreateForm.useCallback[removeImage]\"])\n                    })\n            }[\"PropertyCreateForm.useCallback[removeImage]\"]);\n            // Revoke URL to prevent memory leaks\n            URL.revokeObjectURL(imagePreviewUrls[index]);\n            setImagePreviewUrls({\n                \"PropertyCreateForm.useCallback[removeImage]\": (prev)=>prev.filter({\n                        \"PropertyCreateForm.useCallback[removeImage]\": (_, i)=>i !== index\n                    }[\"PropertyCreateForm.useCallback[removeImage]\"])\n            }[\"PropertyCreateForm.useCallback[removeImage]\"]);\n        }\n    }[\"PropertyCreateForm.useCallback[removeImage]\"], [\n        imagePreviewUrls\n    ]);\n    // Handle drag and drop\n    const handleDragOver = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleDragOver]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n        }\n    }[\"PropertyCreateForm.useCallback[handleDragOver]\"], []);\n    const handleDrop = (0,react__WEBPACK_IMPORTED_MODULE_1__.useCallback)({\n        \"PropertyCreateForm.useCallback[handleDrop]\": (e)=>{\n            e.preventDefault();\n            e.stopPropagation();\n            handleImageChange(e.dataTransfer.files);\n        }\n    }[\"PropertyCreateForm.useCallback[handleDrop]\"], [\n        handleImageChange\n    ]);\n    const validateForm = ()=>{\n        const newErrors = {};\n        // Required fields validation\n        if (!formData.title.trim()) newErrors.title = t('validation.required');\n        if (!formData.description.trim()) newErrors.description = t('validation.required');\n        if (!formData.price || formData.price <= 0) newErrors.price = t('validation.positive');\n        if (!formData.type) newErrors.type = t('validation.required');\n        if (!formData.location.trim()) newErrors.location = t('validation.required');\n        if (!formData.address.trim()) newErrors.address = t('validation.required');\n        if (!formData.city.trim()) newErrors.city = t('validation.required');\n        if (!formData.bedrooms || formData.bedrooms < 0) newErrors.bedrooms = t('validation.positive');\n        if (!formData.bathrooms || formData.bathrooms < 0) newErrors.bathrooms = t('validation.positive');\n        if (!formData.area || formData.area <= 0) newErrors.area = t('validation.positive');\n        setErrors(newErrors);\n        return Object.keys(newErrors).length === 0;\n    };\n    const handleSubmit = async (e)=>{\n        e.preventDefault();\n        if (!validateForm()) {\n            toast({\n                title: t('properties.error'),\n                description: t('validation.required'),\n                variant: 'destructive'\n            });\n            return;\n        }\n        setIsLoading(true);\n        setUploadingImages(true);\n        try {\n            // Upload images first if any\n            let imageUrls = [];\n            if (formData.images.length > 0) {\n                toast({\n                    title: t('images.uploading'),\n                    description: 'جاري رفع الصور...'\n                });\n                // Create FormData for image upload\n                const imageFormData = new FormData();\n                formData.images.forEach((file, index)=>{\n                    imageFormData.append(\"images\", file);\n                });\n                // Here you would typically upload to your image service\n                // For now, we'll create mock URLs\n                imageUrls = formData.images.map((file, index)=>\"https://example.com/images/\".concat(Date.now(), \"-\").concat(index, \"-\").concat(file.name));\n            }\n            const propertyData = {\n                title: formData.title,\n                description: formData.description,\n                price: Number(formData.price),\n                currency: formData.currency,\n                type: formData.type,\n                status: formData.status,\n                bedrooms: Number(formData.bedrooms),\n                bathrooms: Number(formData.bathrooms),\n                area: Number(formData.area),\n                location: formData.location,\n                address: formData.address,\n                city: formData.city,\n                country: formData.country,\n                images: imageUrls,\n                features: formData.features,\n                amenities: formData.amenities,\n                yearBuilt: formData.yearBuilt ? Number(formData.yearBuilt) : undefined,\n                parking: formData.parking ? Number(formData.parking) : undefined,\n                furnished: formData.furnished,\n                petFriendly: formData.petFriendly\n            };\n            await _services_propertyService__WEBPACK_IMPORTED_MODULE_9__.propertyService.createProperty(propertyData);\n            toast({\n                title: t('properties.success'),\n                description: 'تم إنشاء العقار بنجاح'\n            });\n            onSuccess();\n        } catch (error) {\n            console.error('Error creating property:', error);\n            toast({\n                title: t('properties.error'),\n                description: 'حدث خطأ أثناء إنشاء العقار',\n                variant: 'destructive'\n            });\n        } finally{\n            setIsLoading(false);\n            setUploadingImages(false);\n        }\n    };\n    const propertyTypes = [\n        {\n            value: 'APARTMENT',\n            label: t('property.type.apartment')\n        },\n        {\n            value: 'VILLA',\n            label: t('property.type.villa')\n        },\n        {\n            value: 'TOWNHOUSE',\n            label: t('property.type.townhouse')\n        },\n        {\n            value: 'PENTHOUSE',\n            label: t('property.type.penthouse')\n        },\n        {\n            value: 'STUDIO',\n            label: t('property.type.studio')\n        },\n        {\n            value: 'OFFICE',\n            label: t('property.type.office')\n        },\n        {\n            value: 'SHOP',\n            label: t('property.type.shop')\n        },\n        {\n            value: 'WAREHOUSE',\n            label: t('property.type.warehouse')\n        },\n        {\n            value: 'LAND',\n            label: t('property.type.land')\n        },\n        {\n            value: 'BUILDING',\n            label: t('property.type.building')\n        }\n    ];\n    const propertyStatuses = [\n        {\n            value: 'AVAILABLE',\n            label: t('property.status.available')\n        },\n        {\n            value: 'SOLD',\n            label: t('property.status.sold')\n        },\n        {\n            value: 'RENTED',\n            label: t('property.status.rented')\n        },\n        {\n            value: 'PENDING',\n            label: t('property.status.pending')\n        }\n    ];\n    const countries = [\n        {\n            value: 'UAE',\n            label: t('country.uae')\n        },\n        {\n            value: 'SAUDI',\n            label: t('country.saudi')\n        },\n        {\n            value: 'QATAR',\n            label: t('country.qatar')\n        },\n        {\n            value: 'KUWAIT',\n            label: t('country.kuwait')\n        },\n        {\n            value: 'BAHRAIN',\n            label: t('country.bahrain')\n        },\n        {\n            value: 'OMAN',\n            label: t('country.oman')\n        }\n    ];\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"form\", {\n        onSubmit: handleSubmit,\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"المعلومات الأساسية\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 282,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.title'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 286,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.title ? 'error' : ''),\n                                        value: formData.title,\n                                        onChange: (e)=>handleInputChange('title', e.target.value),\n                                        placeholder: t('property.title.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 287,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.title && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.title\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 294,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.price'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 298,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.price ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.price,\n                                        onChange: (e)=>handleInputChange('price', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: t('property.price.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 299,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.price && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.price\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 307,\n                                        columnNumber: 30\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 297,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 284,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.type'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 313,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.type,\n                                        onValueChange: (value)=>handleInputChange('type', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select \".concat(errors.type ? 'error' : ''),\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                                    placeholder: \"اختر نوع العقار\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 316,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 315,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: propertyTypes.map((type)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: type.value,\n                                                        children: type.label\n                                                    }, type.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 320,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 318,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 314,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.type && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.type\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 326,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.status')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 330,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.status,\n                                        onValueChange: (value)=>handleInputChange('status', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 333,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: propertyStatuses.map((status)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: status.value,\n                                                        children: status.label\n                                                    }, status.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 337,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 335,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 331,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 329,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 281,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"تفاصيل العقار\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 349,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-3\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.bedrooms'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 353,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.bedrooms ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.bedrooms,\n                                        onChange: (e)=>handleInputChange('bedrooms', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"0\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.bedrooms\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 352,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.bathrooms'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.bathrooms ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.bathrooms,\n                                        onChange: (e)=>handleInputChange('bathrooms', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"0\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 368,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.bathrooms\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 377,\n                                        columnNumber: 34\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.area'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 381,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.area ? 'error' : ''),\n                                        type: \"number\",\n                                        value: formData.area,\n                                        onChange: (e)=>handleInputChange('area', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"0\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.area\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 391,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 351,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.yearBuilt')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 397,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        type: \"number\",\n                                        value: formData.yearBuilt,\n                                        onChange: (e)=>handleInputChange('yearBuilt', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"2024\",\n                                        min: \"1900\",\n                                        max: \"2030\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 398,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.parking')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 411,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input\",\n                                        type: \"number\",\n                                        value: formData.parking,\n                                        onChange: (e)=>handleInputChange('parking', e.target.value ? Number(e.target.value) : ''),\n                                        placeholder: \"0\",\n                                        min: \"0\",\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 412,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 410,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 395,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 348,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: \"arabic-heading\",\n                        children: \"معلومات الموقع\"\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 427,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.location'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 431,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.location ? 'error' : ''),\n                                        value: formData.location,\n                                        onChange: (e)=>handleInputChange('location', e.target.value),\n                                        placeholder: t('property.location.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 432,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.location\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 439,\n                                        columnNumber: 33\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 430,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.address'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 443,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.address ? 'error' : ''),\n                                        value: formData.address,\n                                        onChange: (e)=>handleInputChange('address', e.target.value),\n                                        placeholder: t('property.address.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 444,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.address && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.address\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 32\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 442,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 429,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.city'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                        className: \"form-input \".concat(errors.city ? 'error' : ''),\n                                        value: formData.city,\n                                        onChange: (e)=>handleInputChange('city', e.target.value),\n                                        placeholder: t('property.city.placeholder'),\n                                        dir: \"rtl\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 458,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.city && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.city\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 465,\n                                        columnNumber: 29\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 456,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: t('property.country')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                                        value: formData.country,\n                                        onValueChange: (value)=>handleInputChange('country', value),\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                                className: \"form-select\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {}, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                    lineNumber: 472,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                                children: countries.map((country)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                                        value: country.value,\n                                                        children: country.label\n                                                    }, country.value, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this))\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                                lineNumber: 474,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 470,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 468,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 455,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 426,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: language === 'ar' ? 'arabic-heading' : '',\n                        children: language === 'ar' ? 'الوصف' : 'Description'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 488,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: [\n                                            t('property.description'),\n                                            \" *\"\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 494,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                        className: \"form-textarea \".concat(errors.description ? 'error' : ''),\n                                        value: formData.description,\n                                        onChange: (e)=>handleInputChange('description', e.target.value),\n                                        placeholder: t('property.description.placeholder'),\n                                        dir: language === 'ar' ? 'rtl' : 'ltr',\n                                        rows: 4\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 495,\n                                        columnNumber: 13\n                                    }, this),\n                                    errors.description && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"form-error\",\n                                        children: errors.description\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 503,\n                                        columnNumber: 36\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 493,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        className: \"form-label\",\n                                        children: language === 'ar' ? 'الوصف بالعربية' : 'Description (Arabic)'\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 507,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_textarea__WEBPACK_IMPORTED_MODULE_6__.Textarea, {\n                                        className: \"form-textarea\",\n                                        value: formData.descriptionAr,\n                                        onChange: (e)=>handleInputChange('descriptionAr', e.target.value),\n                                        placeholder: \"أدخل وصف مفصل للعقار بالعربية\",\n                                        dir: \"rtl\",\n                                        rows: 4\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 508,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 506,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 492,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 487,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"form-section\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                        className: language === 'ar' ? 'arabic-heading' : '',\n                        children: language === 'ar' ? 'خيارات إضافية' : 'Additional Options'\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 522,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"property-grid property-grid-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"furnished\",\n                                        checked: formData.furnished,\n                                        onChange: (e)=>handleInputChange('furnished', e.target.checked),\n                                        className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 528,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"furnished\",\n                                        className: \"form-label\",\n                                        children: t('property.furnished')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 535,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 527,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-2\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"input\", {\n                                        type: \"checkbox\",\n                                        id: \"petFriendly\",\n                                        checked: formData.petFriendly,\n                                        onChange: (e)=>handleInputChange('petFriendly', e.target.checked),\n                                        className: \"w-4 h-4 text-blue-600 bg-gray-100 border-gray-300 rounded focus:ring-blue-500\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 541,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_label__WEBPACK_IMPORTED_MODULE_5__.Label, {\n                                        htmlFor: \"petFriendly\",\n                                        className: \"form-label\",\n                                        children: t('property.petFriendly')\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                        lineNumber: 548,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 540,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 526,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 521,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex gap-4 \".concat(language === 'ar' ? 'flex-row-reverse' : ''),\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"submit\",\n                        disabled: isLoading,\n                        className: \"btn-primary\",\n                        children: [\n                            isLoading && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"loading-spinner\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                                lineNumber: 562,\n                                columnNumber: 25\n                            }, this),\n                            isLoading ? t('properties.loading') : t('properties.save')\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 557,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        type: \"button\",\n                        onClick: onCancel,\n                        disabled: isLoading,\n                        className: \"btn-secondary\",\n                        children: t('properties.cancel')\n                    }, void 0, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                        lineNumber: 566,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n                lineNumber: 556,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyCreateForm.tsx\",\n        lineNumber: 279,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyCreateForm, \"EwYC4OVtcLowLYjR0+a8zoapcI0=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage,\n        _hooks_use_toast__WEBPACK_IMPORTED_MODULE_8__.useToast\n    ];\n});\n_c = PropertyCreateForm;\nvar _c;\n$RefreshReg$(_c, \"PropertyCreateForm\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvcHJvcGVydGllcy9Qcm9wZXJ0eUNyZWF0ZUZvcm0udHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBRXFEO0FBQ1E7QUFDZDtBQUNGO0FBQ0E7QUFDTTtBQUNtRDtBQUMxRDtBQUNnQjtBQWlDckQsU0FBU2UsbUJBQW1CLEtBQXlFO1FBQXpFLEVBQUVDLFNBQVMsRUFBRUMsUUFBUSxFQUFFQyxTQUFTLEVBQUVDLFlBQVksRUFBMkIsR0FBekU7O0lBQ2pDLE1BQU0sRUFBRUMsQ0FBQyxFQUFFLEdBQUdqQiwyRUFBaUJBO0lBQy9CLE1BQU0sRUFBRWtCLEtBQUssRUFBRSxHQUFHUiwwREFBUUE7SUFDMUIsTUFBTVMsZUFBZXBCLDZDQUFNQSxDQUFtQjtJQUU5QyxNQUFNLENBQUNxQixVQUFVQyxZQUFZLEdBQUd4QiwrQ0FBUUEsQ0FBbUI7UUFDekR5QixPQUFPO1FBQ1BDLGFBQWE7UUFDYkMsT0FBTztRQUNQQyxVQUFVO1FBQ1ZDLE1BQU07UUFDTkMsUUFBUTtRQUNSQyxVQUFVO1FBQ1ZDLFdBQVc7UUFDWEMsTUFBTTtRQUNOQyxVQUFVO1FBQ1ZDLFNBQVM7UUFDVEMsTUFBTTtRQUNOQyxTQUFTO1FBQ1RDLFFBQVEsRUFBRTtRQUNWQyxVQUFVLEVBQUU7UUFDWkMsV0FBVyxFQUFFO1FBQ2JDLFdBQVc7UUFDWEMsU0FBUztRQUNUQyxXQUFXO1FBQ1hDLGFBQWE7SUFDZjtJQUVBLE1BQU0sQ0FBQ0MsUUFBUUMsVUFBVSxHQUFHOUMsK0NBQVFBLENBQXlCLENBQUM7SUFDOUQsTUFBTSxDQUFDK0Msa0JBQWtCQyxvQkFBb0IsR0FBR2hELCtDQUFRQSxDQUFXLEVBQUU7SUFDckUsTUFBTSxDQUFDaUQsaUJBQWlCQyxtQkFBbUIsR0FBR2xELCtDQUFRQSxDQUFDO0lBRXZELE1BQU1tRCxvQkFBb0JsRCxrREFBV0E7NkRBQUMsQ0FBQ21ELE9BQStCQztZQUNwRTdCO3FFQUFZOEIsQ0FBQUEsT0FBUzt3QkFBRSxHQUFHQSxJQUFJO3dCQUFFLENBQUNGLE1BQU0sRUFBRUM7b0JBQU07O1lBQy9DLHNDQUFzQztZQUN0QyxJQUFJUixNQUFNLENBQUNPLE1BQU0sRUFBRTtnQkFDakJOO3lFQUFVUSxDQUFBQSxPQUFTOzRCQUFFLEdBQUdBLElBQUk7NEJBQUUsQ0FBQ0YsTUFBTSxFQUFFO3dCQUFHOztZQUM1QztRQUNGOzREQUFHO1FBQUNQO0tBQU87SUFFWCw4QkFBOEI7SUFDOUIsTUFBTVUsb0JBQW9CdEQsa0RBQVdBOzZEQUFDLENBQUN1RDtZQUNyQyxJQUFJLENBQUNBLE9BQU87WUFFWixNQUFNQyxhQUFxQixFQUFFO1lBQzdCLE1BQU1DLGlCQUEyQixFQUFFO1lBRW5DQyxNQUFNQyxJQUFJLENBQUNKLE9BQU9LLE9BQU87cUVBQUNDLENBQUFBO29CQUN4QixxQkFBcUI7b0JBQ3JCLElBQUksQ0FBQ0EsS0FBS2pDLElBQUksQ0FBQ2tDLFVBQVUsQ0FBQyxXQUFXO3dCQUNuQzFDLE1BQU07NEJBQ0pJLE9BQU9MLEVBQUU7NEJBQ1RNLGFBQWE7NEJBQ2JzQyxTQUFTO3dCQUNYO3dCQUNBO29CQUNGO29CQUVBLGdDQUFnQztvQkFDaEMsSUFBSUYsS0FBS0csSUFBSSxHQUFHLEtBQUssT0FBTyxNQUFNO3dCQUNoQzVDLE1BQU07NEJBQ0pJLE9BQU9MLEVBQUU7NEJBQ1RNLGFBQWE7NEJBQ2JzQyxTQUFTO3dCQUNYO3dCQUNBO29CQUNGO29CQUVBUCxXQUFXUyxJQUFJLENBQUNKO29CQUNoQkosZUFBZVEsSUFBSSxDQUFDQyxJQUFJQyxlQUFlLENBQUNOO2dCQUMxQzs7WUFFQSxJQUFJTCxXQUFXWSxNQUFNLEdBQUcsR0FBRztnQkFDekI3Qzt5RUFBWThCLENBQUFBLE9BQVM7NEJBQ25CLEdBQUdBLElBQUk7NEJBQ1BoQixRQUFRO21DQUFJZ0IsS0FBS2hCLE1BQU07bUNBQUttQjs2QkFBVzt3QkFDekM7O2dCQUNBVDt5RUFBb0JNLENBQUFBLE9BQVE7K0JBQUlBOytCQUFTSTt5QkFBZTs7WUFDMUQ7UUFDRjs0REFBRztRQUFDckM7UUFBT0Q7S0FBRTtJQUViLGVBQWU7SUFDZixNQUFNa0QsY0FBY3JFLGtEQUFXQTt1REFBQyxDQUFDc0U7WUFDL0IvQzsrREFBWThCLENBQUFBLE9BQVM7d0JBQ25CLEdBQUdBLElBQUk7d0JBQ1BoQixRQUFRZ0IsS0FBS2hCLE1BQU0sQ0FBQ2tDLE1BQU07MkVBQUMsQ0FBQ0MsR0FBR0MsSUFBTUEsTUFBTUg7O29CQUM3Qzs7WUFFQSxxQ0FBcUM7WUFDckNKLElBQUlRLGVBQWUsQ0FBQzVCLGdCQUFnQixDQUFDd0IsTUFBTTtZQUMzQ3ZCOytEQUFvQk0sQ0FBQUEsT0FBUUEsS0FBS2tCLE1BQU07dUVBQUMsQ0FBQ0MsR0FBR0MsSUFBTUEsTUFBTUg7OztRQUMxRDtzREFBRztRQUFDeEI7S0FBaUI7SUFFckIsdUJBQXVCO0lBQ3ZCLE1BQU02QixpQkFBaUIzRSxrREFBV0E7MERBQUMsQ0FBQzRFO1lBQ2xDQSxFQUFFQyxjQUFjO1lBQ2hCRCxFQUFFRSxlQUFlO1FBQ25CO3lEQUFHLEVBQUU7SUFFTCxNQUFNQyxhQUFhL0Usa0RBQVdBO3NEQUFDLENBQUM0RTtZQUM5QkEsRUFBRUMsY0FBYztZQUNoQkQsRUFBRUUsZUFBZTtZQUNqQnhCLGtCQUFrQnNCLEVBQUVJLFlBQVksQ0FBQ3pCLEtBQUs7UUFDeEM7cURBQUc7UUFBQ0Q7S0FBa0I7SUFFdEIsTUFBTTJCLGVBQWU7UUFDbkIsTUFBTUMsWUFBb0MsQ0FBQztRQUUzQyw2QkFBNkI7UUFDN0IsSUFBSSxDQUFDNUQsU0FBU0UsS0FBSyxDQUFDMkQsSUFBSSxJQUFJRCxVQUFVMUQsS0FBSyxHQUFHTCxFQUFFO1FBQ2hELElBQUksQ0FBQ0csU0FBU0csV0FBVyxDQUFDMEQsSUFBSSxJQUFJRCxVQUFVekQsV0FBVyxHQUFHTixFQUFFO1FBQzVELElBQUksQ0FBQ0csU0FBU0ksS0FBSyxJQUFJSixTQUFTSSxLQUFLLElBQUksR0FBR3dELFVBQVV4RCxLQUFLLEdBQUdQLEVBQUU7UUFDaEUsSUFBSSxDQUFDRyxTQUFTTSxJQUFJLEVBQUVzRCxVQUFVdEQsSUFBSSxHQUFHVCxFQUFFO1FBQ3ZDLElBQUksQ0FBQ0csU0FBU1csUUFBUSxDQUFDa0QsSUFBSSxJQUFJRCxVQUFVakQsUUFBUSxHQUFHZCxFQUFFO1FBQ3RELElBQUksQ0FBQ0csU0FBU1ksT0FBTyxDQUFDaUQsSUFBSSxJQUFJRCxVQUFVaEQsT0FBTyxHQUFHZixFQUFFO1FBQ3BELElBQUksQ0FBQ0csU0FBU2EsSUFBSSxDQUFDZ0QsSUFBSSxJQUFJRCxVQUFVL0MsSUFBSSxHQUFHaEIsRUFBRTtRQUM5QyxJQUFJLENBQUNHLFNBQVNRLFFBQVEsSUFBSVIsU0FBU1EsUUFBUSxHQUFHLEdBQUdvRCxVQUFVcEQsUUFBUSxHQUFHWCxFQUFFO1FBQ3hFLElBQUksQ0FBQ0csU0FBU1MsU0FBUyxJQUFJVCxTQUFTUyxTQUFTLEdBQUcsR0FBR21ELFVBQVVuRCxTQUFTLEdBQUdaLEVBQUU7UUFDM0UsSUFBSSxDQUFDRyxTQUFTVSxJQUFJLElBQUlWLFNBQVNVLElBQUksSUFBSSxHQUFHa0QsVUFBVWxELElBQUksR0FBR2IsRUFBRTtRQUU3RDBCLFVBQVVxQztRQUNWLE9BQU9FLE9BQU9DLElBQUksQ0FBQ0gsV0FBV2QsTUFBTSxLQUFLO0lBQzNDO0lBRUEsTUFBTWtCLGVBQWUsT0FBT1Y7UUFDMUJBLEVBQUVDLGNBQWM7UUFFaEIsSUFBSSxDQUFDSSxnQkFBZ0I7WUFDbkI3RCxNQUFNO2dCQUNKSSxPQUFPTCxFQUFFO2dCQUNUTSxhQUFhTixFQUFFO2dCQUNmNEMsU0FBUztZQUNYO1lBQ0E7UUFDRjtRQUVBN0MsYUFBYTtRQUNiK0IsbUJBQW1CO1FBRW5CLElBQUk7WUFDRiw2QkFBNkI7WUFDN0IsSUFBSXNDLFlBQXNCLEVBQUU7WUFDNUIsSUFBSWpFLFNBQVNlLE1BQU0sQ0FBQytCLE1BQU0sR0FBRyxHQUFHO2dCQUM5QmhELE1BQU07b0JBQ0pJLE9BQU9MLEVBQUU7b0JBQ1RNLGFBQWE7Z0JBQ2Y7Z0JBRUEsbUNBQW1DO2dCQUNuQyxNQUFNK0QsZ0JBQWdCLElBQUlDO2dCQUMxQm5FLFNBQVNlLE1BQU0sQ0FBQ3VCLE9BQU8sQ0FBQyxDQUFDQyxNQUFNUztvQkFDN0JrQixjQUFjRSxNQUFNLENBQUUsVUFBUzdCO2dCQUNqQztnQkFFQSx3REFBd0Q7Z0JBQ3hELGtDQUFrQztnQkFDbEMwQixZQUFZakUsU0FBU2UsTUFBTSxDQUFDc0QsR0FBRyxDQUFDLENBQUM5QixNQUFNUyxRQUNyQyw4QkFBNENBLE9BQWRzQixLQUFLQyxHQUFHLElBQUcsS0FBWWhDLE9BQVRTLE9BQU0sS0FBYSxPQUFWVCxLQUFLaUMsSUFBSTtZQUVsRTtZQUVBLE1BQU1DLGVBQWU7Z0JBQ25CdkUsT0FBT0YsU0FBU0UsS0FBSztnQkFDckJDLGFBQWFILFNBQVNHLFdBQVc7Z0JBQ2pDQyxPQUFPc0UsT0FBTzFFLFNBQVNJLEtBQUs7Z0JBQzVCQyxVQUFVTCxTQUFTSyxRQUFRO2dCQUMzQkMsTUFBTU4sU0FBU00sSUFBSTtnQkFDbkJDLFFBQVFQLFNBQVNPLE1BQU07Z0JBQ3ZCQyxVQUFVa0UsT0FBTzFFLFNBQVNRLFFBQVE7Z0JBQ2xDQyxXQUFXaUUsT0FBTzFFLFNBQVNTLFNBQVM7Z0JBQ3BDQyxNQUFNZ0UsT0FBTzFFLFNBQVNVLElBQUk7Z0JBQzFCQyxVQUFVWCxTQUFTVyxRQUFRO2dCQUMzQkMsU0FBU1osU0FBU1ksT0FBTztnQkFDekJDLE1BQU1iLFNBQVNhLElBQUk7Z0JBQ25CQyxTQUFTZCxTQUFTYyxPQUFPO2dCQUN6QkMsUUFBUWtEO2dCQUNSakQsVUFBVWhCLFNBQVNnQixRQUFRO2dCQUMzQkMsV0FBV2pCLFNBQVNpQixTQUFTO2dCQUM3QkMsV0FBV2xCLFNBQVNrQixTQUFTLEdBQUd3RCxPQUFPMUUsU0FBU2tCLFNBQVMsSUFBSXlEO2dCQUM3RHhELFNBQVNuQixTQUFTbUIsT0FBTyxHQUFHdUQsT0FBTzFFLFNBQVNtQixPQUFPLElBQUl3RDtnQkFDdkR2RCxXQUFXcEIsU0FBU29CLFNBQVM7Z0JBQzdCQyxhQUFhckIsU0FBU3FCLFdBQVc7WUFDbkM7WUFFQSxNQUFNOUIsc0VBQWVBLENBQUNxRixjQUFjLENBQUNIO1lBRXJDM0UsTUFBTTtnQkFDSkksT0FBT0wsRUFBRTtnQkFDVE0sYUFBYTtZQUNmO1lBRUFWO1FBQ0YsRUFBRSxPQUFPb0YsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsNEJBQTRCQTtZQUMxQy9FLE1BQU07Z0JBQ0pJLE9BQU9MLEVBQUU7Z0JBQ1RNLGFBQWE7Z0JBQ2JzQyxTQUFTO1lBQ1g7UUFDRixTQUFVO1lBQ1I3QyxhQUFhO1lBQ2IrQixtQkFBbUI7UUFDckI7SUFDRjtJQUVBLE1BQU1vRCxnQkFBZ0I7UUFDcEI7WUFBRWpELE9BQU87WUFBYWtELE9BQU9uRixFQUFFO1FBQTJCO1FBQzFEO1lBQUVpQyxPQUFPO1lBQVNrRCxPQUFPbkYsRUFBRTtRQUF1QjtRQUNsRDtZQUFFaUMsT0FBTztZQUFha0QsT0FBT25GLEVBQUU7UUFBMkI7UUFDMUQ7WUFBRWlDLE9BQU87WUFBYWtELE9BQU9uRixFQUFFO1FBQTJCO1FBQzFEO1lBQUVpQyxPQUFPO1lBQVVrRCxPQUFPbkYsRUFBRTtRQUF3QjtRQUNwRDtZQUFFaUMsT0FBTztZQUFVa0QsT0FBT25GLEVBQUU7UUFBd0I7UUFDcEQ7WUFBRWlDLE9BQU87WUFBUWtELE9BQU9uRixFQUFFO1FBQXNCO1FBQ2hEO1lBQUVpQyxPQUFPO1lBQWFrRCxPQUFPbkYsRUFBRTtRQUEyQjtRQUMxRDtZQUFFaUMsT0FBTztZQUFRa0QsT0FBT25GLEVBQUU7UUFBc0I7UUFDaEQ7WUFBRWlDLE9BQU87WUFBWWtELE9BQU9uRixFQUFFO1FBQTBCO0tBQ3pEO0lBRUQsTUFBTW9GLG1CQUFtQjtRQUN2QjtZQUFFbkQsT0FBTztZQUFha0QsT0FBT25GLEVBQUU7UUFBNkI7UUFDNUQ7WUFBRWlDLE9BQU87WUFBUWtELE9BQU9uRixFQUFFO1FBQXdCO1FBQ2xEO1lBQUVpQyxPQUFPO1lBQVVrRCxPQUFPbkYsRUFBRTtRQUEwQjtRQUN0RDtZQUFFaUMsT0FBTztZQUFXa0QsT0FBT25GLEVBQUU7UUFBMkI7S0FDekQ7SUFFRCxNQUFNcUYsWUFBWTtRQUNoQjtZQUFFcEQsT0FBTztZQUFPa0QsT0FBT25GLEVBQUU7UUFBZTtRQUN4QztZQUFFaUMsT0FBTztZQUFTa0QsT0FBT25GLEVBQUU7UUFBaUI7UUFDNUM7WUFBRWlDLE9BQU87WUFBU2tELE9BQU9uRixFQUFFO1FBQWlCO1FBQzVDO1lBQUVpQyxPQUFPO1lBQVVrRCxPQUFPbkYsRUFBRTtRQUFrQjtRQUM5QztZQUFFaUMsT0FBTztZQUFXa0QsT0FBT25GLEVBQUU7UUFBbUI7UUFDaEQ7WUFBRWlDLE9BQU87WUFBUWtELE9BQU9uRixFQUFFO1FBQWdCO0tBQzNDO0lBRUQscUJBQ0UsOERBQUNzRjtRQUFLQyxVQUFVcEI7UUFBY3FCLFdBQVU7OzBCQUV0Qyw4REFBQ0M7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDRTt3QkFBR0YsV0FBVTtrQ0FBaUI7Ozs7OztrQ0FFL0IsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7O2tEQUNDLDhEQUFDdkcsdURBQUtBO3dDQUFDc0csV0FBVTs7NENBQWN4RixFQUFFOzRDQUFrQjs7Ozs7OztrREFDbkQsOERBQUNmLHVEQUFLQTt3Q0FDSnVHLFdBQVcsY0FBMEMsT0FBNUIvRCxPQUFPcEIsS0FBSyxHQUFHLFVBQVU7d0NBQ2xENEIsT0FBTzlCLFNBQVNFLEtBQUs7d0NBQ3JCc0YsVUFBVSxDQUFDbEMsSUFBTTFCLGtCQUFrQixTQUFTMEIsRUFBRW1DLE1BQU0sQ0FBQzNELEtBQUs7d0NBQzFENEQsYUFBYTdGLEVBQUU7d0NBQ2Y4RixLQUFJOzs7Ozs7b0NBRUxyRSxPQUFPcEIsS0FBSyxrQkFBSSw4REFBQ29GO3dDQUFJRCxXQUFVO2tEQUFjL0QsT0FBT3BCLEtBQUs7Ozs7Ozs7Ozs7OzswQ0FHNUQsOERBQUNvRjs7a0RBQ0MsOERBQUN2Ryx1REFBS0E7d0NBQUNzRyxXQUFVOzs0Q0FBY3hGLEVBQUU7NENBQWtCOzs7Ozs7O2tEQUNuRCw4REFBQ2YsdURBQUtBO3dDQUNKdUcsV0FBVyxjQUEwQyxPQUE1Qi9ELE9BQU9sQixLQUFLLEdBQUcsVUFBVTt3Q0FDbERFLE1BQUs7d0NBQ0x3QixPQUFPOUIsU0FBU0ksS0FBSzt3Q0FDckJvRixVQUFVLENBQUNsQyxJQUFNMUIsa0JBQWtCLFNBQVMwQixFQUFFbUMsTUFBTSxDQUFDM0QsS0FBSyxHQUFHNEMsT0FBT3BCLEVBQUVtQyxNQUFNLENBQUMzRCxLQUFLLElBQUk7d0NBQ3RGNEQsYUFBYTdGLEVBQUU7d0NBQ2Y4RixLQUFJOzs7Ozs7b0NBRUxyRSxPQUFPbEIsS0FBSyxrQkFBSSw4REFBQ2tGO3dDQUFJRCxXQUFVO2tEQUFjL0QsT0FBT2xCLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJOUQsOERBQUNrRjt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDOztrREFDQyw4REFBQ3ZHLHVEQUFLQTt3Q0FBQ3NHLFdBQVU7OzRDQUFjeEYsRUFBRTs0Q0FBaUI7Ozs7Ozs7a0RBQ2xELDhEQUFDWix5REFBTUE7d0NBQUM2QyxPQUFPOUIsU0FBU00sSUFBSTt3Q0FBRXNGLGVBQWUsQ0FBQzlELFFBQVVGLGtCQUFrQixRQUFRRTs7MERBQ2hGLDhEQUFDMUMsZ0VBQWFBO2dEQUFDaUcsV0FBVyxlQUEwQyxPQUEzQi9ELE9BQU9oQixJQUFJLEdBQUcsVUFBVTswREFDL0QsNEVBQUNqQiw4REFBV0E7b0RBQUNxRyxhQUFZOzs7Ozs7Ozs7OzswREFFM0IsOERBQUN4RyxnRUFBYUE7MERBQ1g2RixjQUFjVixHQUFHLENBQUMsQ0FBQy9ELHFCQUNsQiw4REFBQ25CLDZEQUFVQTt3REFBa0IyQyxPQUFPeEIsS0FBS3dCLEtBQUs7a0VBQzNDeEIsS0FBSzBFLEtBQUs7dURBREkxRSxLQUFLd0IsS0FBSzs7Ozs7Ozs7Ozs7Ozs7OztvQ0FNaENSLE9BQU9oQixJQUFJLGtCQUFJLDhEQUFDZ0Y7d0NBQUlELFdBQVU7a0RBQWMvRCxPQUFPaEIsSUFBSTs7Ozs7Ozs7Ozs7OzBDQUcxRCw4REFBQ2dGOztrREFDQyw4REFBQ3ZHLHVEQUFLQTt3Q0FBQ3NHLFdBQVU7a0RBQWN4RixFQUFFOzs7Ozs7a0RBQ2pDLDhEQUFDWix5REFBTUE7d0NBQUM2QyxPQUFPOUIsU0FBU08sTUFBTTt3Q0FBRXFGLGVBQWUsQ0FBQzlELFFBQVVGLGtCQUFrQixVQUFVRTs7MERBQ3BGLDhEQUFDMUMsZ0VBQWFBO2dEQUFDaUcsV0FBVTswREFDdkIsNEVBQUNoRyw4REFBV0E7Ozs7Ozs7Ozs7MERBRWQsOERBQUNILGdFQUFhQTswREFDWCtGLGlCQUFpQlosR0FBRyxDQUFDLENBQUM5RCx1QkFDckIsOERBQUNwQiw2REFBVUE7d0RBQW9CMkMsT0FBT3ZCLE9BQU91QixLQUFLO2tFQUMvQ3ZCLE9BQU95RSxLQUFLO3VEQURFekUsT0FBT3VCLEtBQUs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBV3pDLDhEQUFDd0Q7Z0JBQUlELFdBQVU7O2tDQUNiLDhEQUFDRTt3QkFBR0YsV0FBVTtrQ0FBaUI7Ozs7OztrQ0FFL0IsOERBQUNDO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7O2tEQUNDLDhEQUFDdkcsdURBQUtBO3dDQUFDc0csV0FBVTs7NENBQWN4RixFQUFFOzRDQUFxQjs7Ozs7OztrREFDdEQsOERBQUNmLHVEQUFLQTt3Q0FDSnVHLFdBQVcsY0FBNkMsT0FBL0IvRCxPQUFPZCxRQUFRLEdBQUcsVUFBVTt3Q0FDckRGLE1BQUs7d0NBQ0x3QixPQUFPOUIsU0FBU1EsUUFBUTt3Q0FDeEJnRixVQUFVLENBQUNsQyxJQUFNMUIsa0JBQWtCLFlBQVkwQixFQUFFbUMsTUFBTSxDQUFDM0QsS0FBSyxHQUFHNEMsT0FBT3BCLEVBQUVtQyxNQUFNLENBQUMzRCxLQUFLLElBQUk7d0NBQ3pGNEQsYUFBWTt3Q0FDWkcsS0FBSTt3Q0FDSkYsS0FBSTs7Ozs7O29DQUVMckUsT0FBT2QsUUFBUSxrQkFBSSw4REFBQzhFO3dDQUFJRCxXQUFVO2tEQUFjL0QsT0FBT2QsUUFBUTs7Ozs7Ozs7Ozs7OzBDQUdsRSw4REFBQzhFOztrREFDQyw4REFBQ3ZHLHVEQUFLQTt3Q0FBQ3NHLFdBQVU7OzRDQUFjeEYsRUFBRTs0Q0FBc0I7Ozs7Ozs7a0RBQ3ZELDhEQUFDZix1REFBS0E7d0NBQ0p1RyxXQUFXLGNBQThDLE9BQWhDL0QsT0FBT2IsU0FBUyxHQUFHLFVBQVU7d0NBQ3RESCxNQUFLO3dDQUNMd0IsT0FBTzlCLFNBQVNTLFNBQVM7d0NBQ3pCK0UsVUFBVSxDQUFDbEMsSUFBTTFCLGtCQUFrQixhQUFhMEIsRUFBRW1DLE1BQU0sQ0FBQzNELEtBQUssR0FBRzRDLE9BQU9wQixFQUFFbUMsTUFBTSxDQUFDM0QsS0FBSyxJQUFJO3dDQUMxRjRELGFBQVk7d0NBQ1pHLEtBQUk7d0NBQ0pGLEtBQUk7Ozs7OztvQ0FFTHJFLE9BQU9iLFNBQVMsa0JBQUksOERBQUM2RTt3Q0FBSUQsV0FBVTtrREFBYy9ELE9BQU9iLFNBQVM7Ozs7Ozs7Ozs7OzswQ0FHcEUsOERBQUM2RTs7a0RBQ0MsOERBQUN2Ryx1REFBS0E7d0NBQUNzRyxXQUFVOzs0Q0FBY3hGLEVBQUU7NENBQWlCOzs7Ozs7O2tEQUNsRCw4REFBQ2YsdURBQUtBO3dDQUNKdUcsV0FBVyxjQUF5QyxPQUEzQi9ELE9BQU9aLElBQUksR0FBRyxVQUFVO3dDQUNqREosTUFBSzt3Q0FDTHdCLE9BQU85QixTQUFTVSxJQUFJO3dDQUNwQjhFLFVBQVUsQ0FBQ2xDLElBQU0xQixrQkFBa0IsUUFBUTBCLEVBQUVtQyxNQUFNLENBQUMzRCxLQUFLLEdBQUc0QyxPQUFPcEIsRUFBRW1DLE1BQU0sQ0FBQzNELEtBQUssSUFBSTt3Q0FDckY0RCxhQUFZO3dDQUNaRyxLQUFJO3dDQUNKRixLQUFJOzs7Ozs7b0NBRUxyRSxPQUFPWixJQUFJLGtCQUFJLDhEQUFDNEU7d0NBQUlELFdBQVU7a0RBQWMvRCxPQUFPWixJQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7a0NBSTVELDhEQUFDNEU7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDQzs7a0RBQ0MsOERBQUN2Ryx1REFBS0E7d0NBQUNzRyxXQUFVO2tEQUFjeEYsRUFBRTs7Ozs7O2tEQUNqQyw4REFBQ2YsdURBQUtBO3dDQUNKdUcsV0FBVTt3Q0FDVi9FLE1BQUs7d0NBQ0x3QixPQUFPOUIsU0FBU2tCLFNBQVM7d0NBQ3pCc0UsVUFBVSxDQUFDbEMsSUFBTTFCLGtCQUFrQixhQUFhMEIsRUFBRW1DLE1BQU0sQ0FBQzNELEtBQUssR0FBRzRDLE9BQU9wQixFQUFFbUMsTUFBTSxDQUFDM0QsS0FBSyxJQUFJO3dDQUMxRjRELGFBQVk7d0NBQ1pHLEtBQUk7d0NBQ0pDLEtBQUk7d0NBQ0pILEtBQUk7Ozs7Ozs7Ozs7OzswQ0FJUiw4REFBQ0w7O2tEQUNDLDhEQUFDdkcsdURBQUtBO3dDQUFDc0csV0FBVTtrREFBY3hGLEVBQUU7Ozs7OztrREFDakMsOERBQUNmLHVEQUFLQTt3Q0FDSnVHLFdBQVU7d0NBQ1YvRSxNQUFLO3dDQUNMd0IsT0FBTzlCLFNBQVNtQixPQUFPO3dDQUN2QnFFLFVBQVUsQ0FBQ2xDLElBQU0xQixrQkFBa0IsV0FBVzBCLEVBQUVtQyxNQUFNLENBQUMzRCxLQUFLLEdBQUc0QyxPQUFPcEIsRUFBRW1DLE1BQU0sQ0FBQzNELEtBQUssSUFBSTt3Q0FDeEY0RCxhQUFZO3dDQUNaRyxLQUFJO3dDQUNKRixLQUFJOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7MEJBT1osOERBQUNMO2dCQUFJRCxXQUFVOztrQ0FDYiw4REFBQ0U7d0JBQUdGLFdBQVU7a0NBQWlCOzs7Ozs7a0NBRS9CLDhEQUFDQzt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDOztrREFDQyw4REFBQ3ZHLHVEQUFLQTt3Q0FBQ3NHLFdBQVU7OzRDQUFjeEYsRUFBRTs0Q0FBcUI7Ozs7Ozs7a0RBQ3RELDhEQUFDZix1REFBS0E7d0NBQ0p1RyxXQUFXLGNBQTZDLE9BQS9CL0QsT0FBT1gsUUFBUSxHQUFHLFVBQVU7d0NBQ3JEbUIsT0FBTzlCLFNBQVNXLFFBQVE7d0NBQ3hCNkUsVUFBVSxDQUFDbEMsSUFBTTFCLGtCQUFrQixZQUFZMEIsRUFBRW1DLE1BQU0sQ0FBQzNELEtBQUs7d0NBQzdENEQsYUFBYTdGLEVBQUU7d0NBQ2Y4RixLQUFJOzs7Ozs7b0NBRUxyRSxPQUFPWCxRQUFRLGtCQUFJLDhEQUFDMkU7d0NBQUlELFdBQVU7a0RBQWMvRCxPQUFPWCxRQUFROzs7Ozs7Ozs7Ozs7MENBR2xFLDhEQUFDMkU7O2tEQUNDLDhEQUFDdkcsdURBQUtBO3dDQUFDc0csV0FBVTs7NENBQWN4RixFQUFFOzRDQUFvQjs7Ozs7OztrREFDckQsOERBQUNmLHVEQUFLQTt3Q0FDSnVHLFdBQVcsY0FBNEMsT0FBOUIvRCxPQUFPVixPQUFPLEdBQUcsVUFBVTt3Q0FDcERrQixPQUFPOUIsU0FBU1ksT0FBTzt3Q0FDdkI0RSxVQUFVLENBQUNsQyxJQUFNMUIsa0JBQWtCLFdBQVcwQixFQUFFbUMsTUFBTSxDQUFDM0QsS0FBSzt3Q0FDNUQ0RCxhQUFhN0YsRUFBRTt3Q0FDZjhGLEtBQUk7Ozs7OztvQ0FFTHJFLE9BQU9WLE9BQU8sa0JBQUksOERBQUMwRTt3Q0FBSUQsV0FBVTtrREFBYy9ELE9BQU9WLE9BQU87Ozs7Ozs7Ozs7Ozs7Ozs7OztrQ0FJbEUsOERBQUMwRTt3QkFBSUQsV0FBVTs7MENBQ2IsOERBQUNDOztrREFDQyw4REFBQ3ZHLHVEQUFLQTt3Q0FBQ3NHLFdBQVU7OzRDQUFjeEYsRUFBRTs0Q0FBaUI7Ozs7Ozs7a0RBQ2xELDhEQUFDZix1REFBS0E7d0NBQ0p1RyxXQUFXLGNBQXlDLE9BQTNCL0QsT0FBT1QsSUFBSSxHQUFHLFVBQVU7d0NBQ2pEaUIsT0FBTzlCLFNBQVNhLElBQUk7d0NBQ3BCMkUsVUFBVSxDQUFDbEMsSUFBTTFCLGtCQUFrQixRQUFRMEIsRUFBRW1DLE1BQU0sQ0FBQzNELEtBQUs7d0NBQ3pENEQsYUFBYTdGLEVBQUU7d0NBQ2Y4RixLQUFJOzs7Ozs7b0NBRUxyRSxPQUFPVCxJQUFJLGtCQUFJLDhEQUFDeUU7d0NBQUlELFdBQVU7a0RBQWMvRCxPQUFPVCxJQUFJOzs7Ozs7Ozs7Ozs7MENBRzFELDhEQUFDeUU7O2tEQUNDLDhEQUFDdkcsdURBQUtBO3dDQUFDc0csV0FBVTtrREFBY3hGLEVBQUU7Ozs7OztrREFDakMsOERBQUNaLHlEQUFNQTt3Q0FBQzZDLE9BQU85QixTQUFTYyxPQUFPO3dDQUFFOEUsZUFBZSxDQUFDOUQsUUFBVUYsa0JBQWtCLFdBQVdFOzswREFDdEYsOERBQUMxQyxnRUFBYUE7Z0RBQUNpRyxXQUFVOzBEQUN2Qiw0RUFBQ2hHLDhEQUFXQTs7Ozs7Ozs7OzswREFFZCw4REFBQ0gsZ0VBQWFBOzBEQUNYZ0csVUFBVWIsR0FBRyxDQUFDLENBQUN2RCx3QkFDZCw4REFBQzNCLDZEQUFVQTt3REFBcUIyQyxPQUFPaEIsUUFBUWdCLEtBQUs7a0VBQ2pEaEIsUUFBUWtFLEtBQUs7dURBRENsRSxRQUFRZ0IsS0FBSzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFXMUMsOERBQUN3RDtnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNFO3dCQUFHRixXQUFXVSxhQUFhLE9BQU8sbUJBQW1CO2tDQUNuREEsYUFBYSxPQUFPLFVBQVU7Ozs7OztrQ0FHakMsOERBQUNUO3dCQUFJRCxXQUFVOzswQ0FDYiw4REFBQ0M7O2tEQUNDLDhEQUFDdkcsdURBQUtBO3dDQUFDc0csV0FBVTs7NENBQWN4RixFQUFFOzRDQUF3Qjs7Ozs7OztrREFDekQsOERBQUNiLDZEQUFRQTt3Q0FDUHFHLFdBQVcsaUJBQW1ELE9BQWxDL0QsT0FBT25CLFdBQVcsR0FBRyxVQUFVO3dDQUMzRDJCLE9BQU85QixTQUFTRyxXQUFXO3dDQUMzQnFGLFVBQVUsQ0FBQ2xDLElBQU0xQixrQkFBa0IsZUFBZTBCLEVBQUVtQyxNQUFNLENBQUMzRCxLQUFLO3dDQUNoRTRELGFBQWE3RixFQUFFO3dDQUNmOEYsS0FBS0ksYUFBYSxPQUFPLFFBQVE7d0NBQ2pDQyxNQUFNOzs7Ozs7b0NBRVAxRSxPQUFPbkIsV0FBVyxrQkFBSSw4REFBQ21GO3dDQUFJRCxXQUFVO2tEQUFjL0QsT0FBT25CLFdBQVc7Ozs7Ozs7Ozs7OzswQ0FHeEUsOERBQUNtRjs7a0RBQ0MsOERBQUN2Ryx1REFBS0E7d0NBQUNzRyxXQUFVO2tEQUFjVSxhQUFhLE9BQU8sbUJBQW1COzs7Ozs7a0RBQ3RFLDhEQUFDL0csNkRBQVFBO3dDQUNQcUcsV0FBVTt3Q0FDVnZELE9BQU85QixTQUFTaUcsYUFBYTt3Q0FDN0JULFVBQVUsQ0FBQ2xDLElBQU0xQixrQkFBa0IsaUJBQWlCMEIsRUFBRW1DLE1BQU0sQ0FBQzNELEtBQUs7d0NBQ2xFNEQsYUFBWTt3Q0FDWkMsS0FBSTt3Q0FDSkssTUFBTTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzBCQU9kLDhEQUFDVjtnQkFBSUQsV0FBVTs7a0NBQ2IsOERBQUNFO3dCQUFHRixXQUFXVSxhQUFhLE9BQU8sbUJBQW1CO2tDQUNuREEsYUFBYSxPQUFPLGtCQUFrQjs7Ozs7O2tDQUd6Qyw4REFBQ1Q7d0JBQUlELFdBQVU7OzBDQUNiLDhEQUFDQztnQ0FBSUQsV0FBVTs7a0RBQ2IsOERBQUNhO3dDQUNDNUYsTUFBSzt3Q0FDTDZGLElBQUc7d0NBQ0hDLFNBQVNwRyxTQUFTb0IsU0FBUzt3Q0FDM0JvRSxVQUFVLENBQUNsQyxJQUFNMUIsa0JBQWtCLGFBQWEwQixFQUFFbUMsTUFBTSxDQUFDVyxPQUFPO3dDQUNoRWYsV0FBVTs7Ozs7O2tEQUVaLDhEQUFDdEcsdURBQUtBO3dDQUFDc0gsU0FBUTt3Q0FBWWhCLFdBQVU7a0RBQ2xDeEYsRUFBRTs7Ozs7Ozs7Ozs7OzBDQUlQLDhEQUFDeUY7Z0NBQUlELFdBQVU7O2tEQUNiLDhEQUFDYTt3Q0FDQzVGLE1BQUs7d0NBQ0w2RixJQUFHO3dDQUNIQyxTQUFTcEcsU0FBU3FCLFdBQVc7d0NBQzdCbUUsVUFBVSxDQUFDbEMsSUFBTTFCLGtCQUFrQixlQUFlMEIsRUFBRW1DLE1BQU0sQ0FBQ1csT0FBTzt3Q0FDbEVmLFdBQVU7Ozs7OztrREFFWiw4REFBQ3RHLHVEQUFLQTt3Q0FBQ3NILFNBQVE7d0NBQWNoQixXQUFVO2tEQUNwQ3hGLEVBQUU7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OzswQkFPWCw4REFBQ3lGO2dCQUFJRCxXQUFXLGNBQTBELE9BQTVDVSxhQUFhLE9BQU8scUJBQXFCOztrQ0FDckUsOERBQUNsSCx5REFBTUE7d0JBQ0x5QixNQUFLO3dCQUNMZ0csVUFBVTNHO3dCQUNWMEYsV0FBVTs7NEJBRVQxRiwyQkFBYSw4REFBQzJGO2dDQUFJRCxXQUFVOzs7Ozs7NEJBQzVCMUYsWUFBWUUsRUFBRSx3QkFBd0JBLEVBQUU7Ozs7Ozs7a0NBRzNDLDhEQUFDaEIseURBQU1BO3dCQUNMeUIsTUFBSzt3QkFDTGlHLFNBQVM3Rzt3QkFDVDRHLFVBQVUzRzt3QkFDVjBGLFdBQVU7a0NBRVR4RixFQUFFOzs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFLYjtHQXJoQmdCTDs7UUFDQVosdUVBQWlCQTtRQUNiVSxzREFBUUE7OztLQUZaRSIsInNvdXJjZXMiOlsiQzpcXFVzZXJzXFxBaG1lZFxcRGVza3RvcFxcY29kZVxcYm9vdFxcZGFzaGJvYXJkXFxjb21wb25lbnRzXFxwcm9wZXJ0aWVzXFxQcm9wZXJ0eUNyZWF0ZUZvcm0udHN4Il0sInNvdXJjZXNDb250ZW50IjpbIlwidXNlIGNsaWVudFwiXG5cbmltcG9ydCB7IHVzZVN0YXRlLCB1c2VDYWxsYmFjaywgdXNlUmVmIH0gZnJvbSAncmVhY3QnXG5pbXBvcnQgeyB1c2VTaW1wbGVMYW5ndWFnZSB9IGZyb20gJ0AvaG9va3MvdXNlU2ltcGxlTGFuZ3VhZ2UnXG5pbXBvcnQgeyBCdXR0b24gfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvYnV0dG9uJ1xuaW1wb3J0IHsgSW5wdXQgfSBmcm9tICdAL2NvbXBvbmVudHMvdWkvaW5wdXQnXG5pbXBvcnQgeyBMYWJlbCB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9sYWJlbCdcbmltcG9ydCB7IFRleHRhcmVhIH0gZnJvbSAnQC9jb21wb25lbnRzL3VpL3RleHRhcmVhJ1xuaW1wb3J0IHsgU2VsZWN0LCBTZWxlY3RDb250ZW50LCBTZWxlY3RJdGVtLCBTZWxlY3RUcmlnZ2VyLCBTZWxlY3RWYWx1ZSB9IGZyb20gJ0AvY29tcG9uZW50cy91aS9zZWxlY3QnXG5pbXBvcnQgeyB1c2VUb2FzdCB9IGZyb20gJ0AvaG9va3MvdXNlLXRvYXN0J1xuaW1wb3J0IHsgcHJvcGVydHlTZXJ2aWNlIH0gZnJvbSAnQC9zZXJ2aWNlcy9wcm9wZXJ0eVNlcnZpY2UnXG5pbXBvcnQgeyBVcGxvYWQsIFgsIEltYWdlIGFzIEltYWdlSWNvbiB9IGZyb20gJ2x1Y2lkZS1yZWFjdCdcblxuaW50ZXJmYWNlIFByb3BlcnR5Rm9ybURhdGEge1xuICB0aXRsZTogc3RyaW5nXG4gIGRlc2NyaXB0aW9uOiBzdHJpbmdcbiAgcHJpY2U6IG51bWJlciB8ICcnXG4gIGN1cnJlbmN5OiBzdHJpbmdcbiAgdHlwZTogc3RyaW5nXG4gIHN0YXR1czogc3RyaW5nXG4gIGJlZHJvb21zOiBudW1iZXIgfCAnJ1xuICBiYXRocm9vbXM6IG51bWJlciB8ICcnXG4gIGFyZWE6IG51bWJlciB8ICcnXG4gIGxvY2F0aW9uOiBzdHJpbmdcbiAgYWRkcmVzczogc3RyaW5nXG4gIGNpdHk6IHN0cmluZ1xuICBjb3VudHJ5OiBzdHJpbmdcbiAgaW1hZ2VzOiBGaWxlW11cbiAgZmVhdHVyZXM6IHN0cmluZ1tdXG4gIGFtZW5pdGllczogc3RyaW5nW11cbiAgeWVhckJ1aWx0OiBudW1iZXIgfCAnJ1xuICBwYXJraW5nOiBudW1iZXIgfCAnJ1xuICBmdXJuaXNoZWQ6IGJvb2xlYW5cbiAgcGV0RnJpZW5kbHk6IGJvb2xlYW5cbn1cblxuaW50ZXJmYWNlIFByb3BlcnR5Q3JlYXRlRm9ybVByb3BzIHtcbiAgb25TdWNjZXNzOiAoKSA9PiB2b2lkXG4gIG9uQ2FuY2VsOiAoKSA9PiB2b2lkXG4gIGlzTG9hZGluZzogYm9vbGVhblxuICBzZXRJc0xvYWRpbmc6IChsb2FkaW5nOiBib29sZWFuKSA9PiB2b2lkXG59XG5cbmV4cG9ydCBmdW5jdGlvbiBQcm9wZXJ0eUNyZWF0ZUZvcm0oeyBvblN1Y2Nlc3MsIG9uQ2FuY2VsLCBpc0xvYWRpbmcsIHNldElzTG9hZGluZyB9OiBQcm9wZXJ0eUNyZWF0ZUZvcm1Qcm9wcykge1xuICBjb25zdCB7IHQgfSA9IHVzZVNpbXBsZUxhbmd1YWdlKClcbiAgY29uc3QgeyB0b2FzdCB9ID0gdXNlVG9hc3QoKVxuICBjb25zdCBmaWxlSW5wdXRSZWYgPSB1c2VSZWY8SFRNTElucHV0RWxlbWVudD4obnVsbClcblxuICBjb25zdCBbZm9ybURhdGEsIHNldEZvcm1EYXRhXSA9IHVzZVN0YXRlPFByb3BlcnR5Rm9ybURhdGE+KHtcbiAgICB0aXRsZTogJycsXG4gICAgZGVzY3JpcHRpb246ICcnLFxuICAgIHByaWNlOiAnJyxcbiAgICBjdXJyZW5jeTogJ1VTRCcsXG4gICAgdHlwZTogJycsXG4gICAgc3RhdHVzOiAnQVZBSUxBQkxFJyxcbiAgICBiZWRyb29tczogJycsXG4gICAgYmF0aHJvb21zOiAnJyxcbiAgICBhcmVhOiAnJyxcbiAgICBsb2NhdGlvbjogJycsXG4gICAgYWRkcmVzczogJycsXG4gICAgY2l0eTogJycsXG4gICAgY291bnRyeTogJ1VBRScsXG4gICAgaW1hZ2VzOiBbXSxcbiAgICBmZWF0dXJlczogW10sXG4gICAgYW1lbml0aWVzOiBbXSxcbiAgICB5ZWFyQnVpbHQ6ICcnLFxuICAgIHBhcmtpbmc6ICcnLFxuICAgIGZ1cm5pc2hlZDogZmFsc2UsXG4gICAgcGV0RnJpZW5kbHk6IGZhbHNlLFxuICB9KVxuXG4gIGNvbnN0IFtlcnJvcnMsIHNldEVycm9yc10gPSB1c2VTdGF0ZTxSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+Pih7fSlcbiAgY29uc3QgW2ltYWdlUHJldmlld1VybHMsIHNldEltYWdlUHJldmlld1VybHNdID0gdXNlU3RhdGU8c3RyaW5nW10+KFtdKVxuICBjb25zdCBbdXBsb2FkaW5nSW1hZ2VzLCBzZXRVcGxvYWRpbmdJbWFnZXNdID0gdXNlU3RhdGUoZmFsc2UpXG5cbiAgY29uc3QgaGFuZGxlSW5wdXRDaGFuZ2UgPSB1c2VDYWxsYmFjaygoZmllbGQ6IGtleW9mIFByb3BlcnR5Rm9ybURhdGEsIHZhbHVlOiBhbnkpID0+IHtcbiAgICBzZXRGb3JtRGF0YShwcmV2ID0+ICh7IC4uLnByZXYsIFtmaWVsZF06IHZhbHVlIH0pKVxuICAgIC8vIENsZWFyIGVycm9yIHdoZW4gdXNlciBzdGFydHMgdHlwaW5nXG4gICAgaWYgKGVycm9yc1tmaWVsZF0pIHtcbiAgICAgIHNldEVycm9ycyhwcmV2ID0+ICh7IC4uLnByZXYsIFtmaWVsZF06ICcnIH0pKVxuICAgIH1cbiAgfSwgW2Vycm9yc10pXG5cbiAgLy8gSGFuZGxlIGltYWdlIGZpbGUgc2VsZWN0aW9uXG4gIGNvbnN0IGhhbmRsZUltYWdlQ2hhbmdlID0gdXNlQ2FsbGJhY2soKGZpbGVzOiBGaWxlTGlzdCB8IG51bGwpID0+IHtcbiAgICBpZiAoIWZpbGVzKSByZXR1cm5cblxuICAgIGNvbnN0IHZhbGlkRmlsZXM6IEZpbGVbXSA9IFtdXG4gICAgY29uc3QgbmV3UHJldmlld1VybHM6IHN0cmluZ1tdID0gW11cblxuICAgIEFycmF5LmZyb20oZmlsZXMpLmZvckVhY2goZmlsZSA9PiB7XG4gICAgICAvLyBWYWxpZGF0ZSBmaWxlIHR5cGVcbiAgICAgIGlmICghZmlsZS50eXBlLnN0YXJ0c1dpdGgoJ2ltYWdlLycpKSB7XG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogdCgnaW1hZ2VzLmVycm9yJyksXG4gICAgICAgICAgZGVzY3JpcHRpb246ICfZitix2KzZiSDYp9iu2KrZitin2LEg2YXZhNmB2KfYqiDYtdmI2LEg2YHZgti3JyxcbiAgICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnLFxuICAgICAgICB9KVxuICAgICAgICByZXR1cm5cbiAgICAgIH1cblxuICAgICAgLy8gVmFsaWRhdGUgZmlsZSBzaXplICgxME1CIG1heClcbiAgICAgIGlmIChmaWxlLnNpemUgPiAxMCAqIDEwMjQgKiAxMDI0KSB7XG4gICAgICAgIHRvYXN0KHtcbiAgICAgICAgICB0aXRsZTogdCgnaW1hZ2VzLmVycm9yJyksXG4gICAgICAgICAgZGVzY3JpcHRpb246ICfYrdis2YUg2KfZhNi12YjYsdipINmK2KzYqCDYo9mGINmK2YPZiNmGINij2YLZhCDZhdmGIDEwTUInLFxuICAgICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXG4gICAgICAgIH0pXG4gICAgICAgIHJldHVyblxuICAgICAgfVxuXG4gICAgICB2YWxpZEZpbGVzLnB1c2goZmlsZSlcbiAgICAgIG5ld1ByZXZpZXdVcmxzLnB1c2goVVJMLmNyZWF0ZU9iamVjdFVSTChmaWxlKSlcbiAgICB9KVxuXG4gICAgaWYgKHZhbGlkRmlsZXMubGVuZ3RoID4gMCkge1xuICAgICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgICAuLi5wcmV2LFxuICAgICAgICBpbWFnZXM6IFsuLi5wcmV2LmltYWdlcywgLi4udmFsaWRGaWxlc11cbiAgICAgIH0pKVxuICAgICAgc2V0SW1hZ2VQcmV2aWV3VXJscyhwcmV2ID0+IFsuLi5wcmV2LCAuLi5uZXdQcmV2aWV3VXJsc10pXG4gICAgfVxuICB9LCBbdG9hc3QsIHRdKVxuXG4gIC8vIFJlbW92ZSBpbWFnZVxuICBjb25zdCByZW1vdmVJbWFnZSA9IHVzZUNhbGxiYWNrKChpbmRleDogbnVtYmVyKSA9PiB7XG4gICAgc2V0Rm9ybURhdGEocHJldiA9PiAoe1xuICAgICAgLi4ucHJldixcbiAgICAgIGltYWdlczogcHJldi5pbWFnZXMuZmlsdGVyKChfLCBpKSA9PiBpICE9PSBpbmRleClcbiAgICB9KSlcblxuICAgIC8vIFJldm9rZSBVUkwgdG8gcHJldmVudCBtZW1vcnkgbGVha3NcbiAgICBVUkwucmV2b2tlT2JqZWN0VVJMKGltYWdlUHJldmlld1VybHNbaW5kZXhdKVxuICAgIHNldEltYWdlUHJldmlld1VybHMocHJldiA9PiBwcmV2LmZpbHRlcigoXywgaSkgPT4gaSAhPT0gaW5kZXgpKVxuICB9LCBbaW1hZ2VQcmV2aWV3VXJsc10pXG5cbiAgLy8gSGFuZGxlIGRyYWcgYW5kIGRyb3BcbiAgY29uc3QgaGFuZGxlRHJhZ092ZXIgPSB1c2VDYWxsYmFjaygoZTogUmVhY3QuRHJhZ0V2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpXG4gICAgZS5zdG9wUHJvcGFnYXRpb24oKVxuICB9LCBbXSlcblxuICBjb25zdCBoYW5kbGVEcm9wID0gdXNlQ2FsbGJhY2soKGU6IFJlYWN0LkRyYWdFdmVudCkgPT4ge1xuICAgIGUucHJldmVudERlZmF1bHQoKVxuICAgIGUuc3RvcFByb3BhZ2F0aW9uKClcbiAgICBoYW5kbGVJbWFnZUNoYW5nZShlLmRhdGFUcmFuc2Zlci5maWxlcylcbiAgfSwgW2hhbmRsZUltYWdlQ2hhbmdlXSlcblxuICBjb25zdCB2YWxpZGF0ZUZvcm0gPSAoKTogYm9vbGVhbiA9PiB7XG4gICAgY29uc3QgbmV3RXJyb3JzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge31cblxuICAgIC8vIFJlcXVpcmVkIGZpZWxkcyB2YWxpZGF0aW9uXG4gICAgaWYgKCFmb3JtRGF0YS50aXRsZS50cmltKCkpIG5ld0Vycm9ycy50aXRsZSA9IHQoJ3ZhbGlkYXRpb24ucmVxdWlyZWQnKVxuICAgIGlmICghZm9ybURhdGEuZGVzY3JpcHRpb24udHJpbSgpKSBuZXdFcnJvcnMuZGVzY3JpcHRpb24gPSB0KCd2YWxpZGF0aW9uLnJlcXVpcmVkJylcbiAgICBpZiAoIWZvcm1EYXRhLnByaWNlIHx8IGZvcm1EYXRhLnByaWNlIDw9IDApIG5ld0Vycm9ycy5wcmljZSA9IHQoJ3ZhbGlkYXRpb24ucG9zaXRpdmUnKVxuICAgIGlmICghZm9ybURhdGEudHlwZSkgbmV3RXJyb3JzLnR5cGUgPSB0KCd2YWxpZGF0aW9uLnJlcXVpcmVkJylcbiAgICBpZiAoIWZvcm1EYXRhLmxvY2F0aW9uLnRyaW0oKSkgbmV3RXJyb3JzLmxvY2F0aW9uID0gdCgndmFsaWRhdGlvbi5yZXF1aXJlZCcpXG4gICAgaWYgKCFmb3JtRGF0YS5hZGRyZXNzLnRyaW0oKSkgbmV3RXJyb3JzLmFkZHJlc3MgPSB0KCd2YWxpZGF0aW9uLnJlcXVpcmVkJylcbiAgICBpZiAoIWZvcm1EYXRhLmNpdHkudHJpbSgpKSBuZXdFcnJvcnMuY2l0eSA9IHQoJ3ZhbGlkYXRpb24ucmVxdWlyZWQnKVxuICAgIGlmICghZm9ybURhdGEuYmVkcm9vbXMgfHwgZm9ybURhdGEuYmVkcm9vbXMgPCAwKSBuZXdFcnJvcnMuYmVkcm9vbXMgPSB0KCd2YWxpZGF0aW9uLnBvc2l0aXZlJylcbiAgICBpZiAoIWZvcm1EYXRhLmJhdGhyb29tcyB8fCBmb3JtRGF0YS5iYXRocm9vbXMgPCAwKSBuZXdFcnJvcnMuYmF0aHJvb21zID0gdCgndmFsaWRhdGlvbi5wb3NpdGl2ZScpXG4gICAgaWYgKCFmb3JtRGF0YS5hcmVhIHx8IGZvcm1EYXRhLmFyZWEgPD0gMCkgbmV3RXJyb3JzLmFyZWEgPSB0KCd2YWxpZGF0aW9uLnBvc2l0aXZlJylcblxuICAgIHNldEVycm9ycyhuZXdFcnJvcnMpXG4gICAgcmV0dXJuIE9iamVjdC5rZXlzKG5ld0Vycm9ycykubGVuZ3RoID09PSAwXG4gIH1cblxuICBjb25zdCBoYW5kbGVTdWJtaXQgPSBhc3luYyAoZTogUmVhY3QuRm9ybUV2ZW50KSA9PiB7XG4gICAgZS5wcmV2ZW50RGVmYXVsdCgpXG5cbiAgICBpZiAoIXZhbGlkYXRlRm9ybSgpKSB7XG4gICAgICB0b2FzdCh7XG4gICAgICAgIHRpdGxlOiB0KCdwcm9wZXJ0aWVzLmVycm9yJyksXG4gICAgICAgIGRlc2NyaXB0aW9uOiB0KCd2YWxpZGF0aW9uLnJlcXVpcmVkJyksXG4gICAgICAgIHZhcmlhbnQ6ICdkZXN0cnVjdGl2ZScsXG4gICAgICB9KVxuICAgICAgcmV0dXJuXG4gICAgfVxuXG4gICAgc2V0SXNMb2FkaW5nKHRydWUpXG4gICAgc2V0VXBsb2FkaW5nSW1hZ2VzKHRydWUpXG5cbiAgICB0cnkge1xuICAgICAgLy8gVXBsb2FkIGltYWdlcyBmaXJzdCBpZiBhbnlcbiAgICAgIGxldCBpbWFnZVVybHM6IHN0cmluZ1tdID0gW11cbiAgICAgIGlmIChmb3JtRGF0YS5pbWFnZXMubGVuZ3RoID4gMCkge1xuICAgICAgICB0b2FzdCh7XG4gICAgICAgICAgdGl0bGU6IHQoJ2ltYWdlcy51cGxvYWRpbmcnKSxcbiAgICAgICAgICBkZXNjcmlwdGlvbjogJ9is2KfYsdmKINix2YHYuSDYp9mE2LXZiNixLi4uJyxcbiAgICAgICAgfSlcblxuICAgICAgICAvLyBDcmVhdGUgRm9ybURhdGEgZm9yIGltYWdlIHVwbG9hZFxuICAgICAgICBjb25zdCBpbWFnZUZvcm1EYXRhID0gbmV3IEZvcm1EYXRhKClcbiAgICAgICAgZm9ybURhdGEuaW1hZ2VzLmZvckVhY2goKGZpbGUsIGluZGV4KSA9PiB7XG4gICAgICAgICAgaW1hZ2VGb3JtRGF0YS5hcHBlbmQoYGltYWdlc2AsIGZpbGUpXG4gICAgICAgIH0pXG5cbiAgICAgICAgLy8gSGVyZSB5b3Ugd291bGQgdHlwaWNhbGx5IHVwbG9hZCB0byB5b3VyIGltYWdlIHNlcnZpY2VcbiAgICAgICAgLy8gRm9yIG5vdywgd2UnbGwgY3JlYXRlIG1vY2sgVVJMc1xuICAgICAgICBpbWFnZVVybHMgPSBmb3JtRGF0YS5pbWFnZXMubWFwKChmaWxlLCBpbmRleCkgPT5cbiAgICAgICAgICBgaHR0cHM6Ly9leGFtcGxlLmNvbS9pbWFnZXMvJHtEYXRlLm5vdygpfS0ke2luZGV4fS0ke2ZpbGUubmFtZX1gXG4gICAgICAgIClcbiAgICAgIH1cblxuICAgICAgY29uc3QgcHJvcGVydHlEYXRhID0ge1xuICAgICAgICB0aXRsZTogZm9ybURhdGEudGl0bGUsXG4gICAgICAgIGRlc2NyaXB0aW9uOiBmb3JtRGF0YS5kZXNjcmlwdGlvbixcbiAgICAgICAgcHJpY2U6IE51bWJlcihmb3JtRGF0YS5wcmljZSksXG4gICAgICAgIGN1cnJlbmN5OiBmb3JtRGF0YS5jdXJyZW5jeSxcbiAgICAgICAgdHlwZTogZm9ybURhdGEudHlwZSxcbiAgICAgICAgc3RhdHVzOiBmb3JtRGF0YS5zdGF0dXMsXG4gICAgICAgIGJlZHJvb21zOiBOdW1iZXIoZm9ybURhdGEuYmVkcm9vbXMpLFxuICAgICAgICBiYXRocm9vbXM6IE51bWJlcihmb3JtRGF0YS5iYXRocm9vbXMpLFxuICAgICAgICBhcmVhOiBOdW1iZXIoZm9ybURhdGEuYXJlYSksXG4gICAgICAgIGxvY2F0aW9uOiBmb3JtRGF0YS5sb2NhdGlvbixcbiAgICAgICAgYWRkcmVzczogZm9ybURhdGEuYWRkcmVzcyxcbiAgICAgICAgY2l0eTogZm9ybURhdGEuY2l0eSxcbiAgICAgICAgY291bnRyeTogZm9ybURhdGEuY291bnRyeSxcbiAgICAgICAgaW1hZ2VzOiBpbWFnZVVybHMsXG4gICAgICAgIGZlYXR1cmVzOiBmb3JtRGF0YS5mZWF0dXJlcyxcbiAgICAgICAgYW1lbml0aWVzOiBmb3JtRGF0YS5hbWVuaXRpZXMsXG4gICAgICAgIHllYXJCdWlsdDogZm9ybURhdGEueWVhckJ1aWx0ID8gTnVtYmVyKGZvcm1EYXRhLnllYXJCdWlsdCkgOiB1bmRlZmluZWQsXG4gICAgICAgIHBhcmtpbmc6IGZvcm1EYXRhLnBhcmtpbmcgPyBOdW1iZXIoZm9ybURhdGEucGFya2luZykgOiB1bmRlZmluZWQsXG4gICAgICAgIGZ1cm5pc2hlZDogZm9ybURhdGEuZnVybmlzaGVkLFxuICAgICAgICBwZXRGcmllbmRseTogZm9ybURhdGEucGV0RnJpZW5kbHksXG4gICAgICB9XG5cbiAgICAgIGF3YWl0IHByb3BlcnR5U2VydmljZS5jcmVhdGVQcm9wZXJ0eShwcm9wZXJ0eURhdGEpXG5cbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IHQoJ3Byb3BlcnRpZXMuc3VjY2VzcycpLFxuICAgICAgICBkZXNjcmlwdGlvbjogJ9iq2YUg2KXZhti02KfYoSDYp9mE2LnZgtin2LEg2KjZhtis2KfYrScsXG4gICAgICB9KVxuXG4gICAgICBvblN1Y2Nlc3MoKVxuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdFcnJvciBjcmVhdGluZyBwcm9wZXJ0eTonLCBlcnJvcilcbiAgICAgIHRvYXN0KHtcbiAgICAgICAgdGl0bGU6IHQoJ3Byb3BlcnRpZXMuZXJyb3InKSxcbiAgICAgICAgZGVzY3JpcHRpb246ICfYrdiv2Ksg2K7Yt9ijINij2KvZhtin2KEg2KXZhti02KfYoSDYp9mE2LnZgtin2LEnLFxuICAgICAgICB2YXJpYW50OiAnZGVzdHJ1Y3RpdmUnLFxuICAgICAgfSlcbiAgICB9IGZpbmFsbHkge1xuICAgICAgc2V0SXNMb2FkaW5nKGZhbHNlKVxuICAgICAgc2V0VXBsb2FkaW5nSW1hZ2VzKGZhbHNlKVxuICAgIH1cbiAgfVxuXG4gIGNvbnN0IHByb3BlcnR5VHlwZXMgPSBbXG4gICAgeyB2YWx1ZTogJ0FQQVJUTUVOVCcsIGxhYmVsOiB0KCdwcm9wZXJ0eS50eXBlLmFwYXJ0bWVudCcpIH0sXG4gICAgeyB2YWx1ZTogJ1ZJTExBJywgbGFiZWw6IHQoJ3Byb3BlcnR5LnR5cGUudmlsbGEnKSB9LFxuICAgIHsgdmFsdWU6ICdUT1dOSE9VU0UnLCBsYWJlbDogdCgncHJvcGVydHkudHlwZS50b3duaG91c2UnKSB9LFxuICAgIHsgdmFsdWU6ICdQRU5USE9VU0UnLCBsYWJlbDogdCgncHJvcGVydHkudHlwZS5wZW50aG91c2UnKSB9LFxuICAgIHsgdmFsdWU6ICdTVFVESU8nLCBsYWJlbDogdCgncHJvcGVydHkudHlwZS5zdHVkaW8nKSB9LFxuICAgIHsgdmFsdWU6ICdPRkZJQ0UnLCBsYWJlbDogdCgncHJvcGVydHkudHlwZS5vZmZpY2UnKSB9LFxuICAgIHsgdmFsdWU6ICdTSE9QJywgbGFiZWw6IHQoJ3Byb3BlcnR5LnR5cGUuc2hvcCcpIH0sXG4gICAgeyB2YWx1ZTogJ1dBUkVIT1VTRScsIGxhYmVsOiB0KCdwcm9wZXJ0eS50eXBlLndhcmVob3VzZScpIH0sXG4gICAgeyB2YWx1ZTogJ0xBTkQnLCBsYWJlbDogdCgncHJvcGVydHkudHlwZS5sYW5kJykgfSxcbiAgICB7IHZhbHVlOiAnQlVJTERJTkcnLCBsYWJlbDogdCgncHJvcGVydHkudHlwZS5idWlsZGluZycpIH0sXG4gIF1cblxuICBjb25zdCBwcm9wZXJ0eVN0YXR1c2VzID0gW1xuICAgIHsgdmFsdWU6ICdBVkFJTEFCTEUnLCBsYWJlbDogdCgncHJvcGVydHkuc3RhdHVzLmF2YWlsYWJsZScpIH0sXG4gICAgeyB2YWx1ZTogJ1NPTEQnLCBsYWJlbDogdCgncHJvcGVydHkuc3RhdHVzLnNvbGQnKSB9LFxuICAgIHsgdmFsdWU6ICdSRU5URUQnLCBsYWJlbDogdCgncHJvcGVydHkuc3RhdHVzLnJlbnRlZCcpIH0sXG4gICAgeyB2YWx1ZTogJ1BFTkRJTkcnLCBsYWJlbDogdCgncHJvcGVydHkuc3RhdHVzLnBlbmRpbmcnKSB9LFxuICBdXG5cbiAgY29uc3QgY291bnRyaWVzID0gW1xuICAgIHsgdmFsdWU6ICdVQUUnLCBsYWJlbDogdCgnY291bnRyeS51YWUnKSB9LFxuICAgIHsgdmFsdWU6ICdTQVVESScsIGxhYmVsOiB0KCdjb3VudHJ5LnNhdWRpJykgfSxcbiAgICB7IHZhbHVlOiAnUUFUQVInLCBsYWJlbDogdCgnY291bnRyeS5xYXRhcicpIH0sXG4gICAgeyB2YWx1ZTogJ0tVV0FJVCcsIGxhYmVsOiB0KCdjb3VudHJ5Lmt1d2FpdCcpIH0sXG4gICAgeyB2YWx1ZTogJ0JBSFJBSU4nLCBsYWJlbDogdCgnY291bnRyeS5iYWhyYWluJykgfSxcbiAgICB7IHZhbHVlOiAnT01BTicsIGxhYmVsOiB0KCdjb3VudHJ5Lm9tYW4nKSB9LFxuICBdXG5cbiAgcmV0dXJuIChcbiAgICA8Zm9ybSBvblN1Ym1pdD17aGFuZGxlU3VibWl0fSBjbGFzc05hbWU9XCJzcGFjZS15LTZcIj5cbiAgICAgIHsvKiBCYXNpYyBJbmZvcm1hdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1zZWN0aW9uXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJhcmFiaWMtaGVhZGluZ1wiPtin2YTZhdi52YTZiNmF2KfYqiDYp9mE2KPYs9in2LPZitipPC9oMz5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInByb3BlcnR5LWdyaWQgcHJvcGVydHktZ3JpZC0yXCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJmb3JtLWxhYmVsXCI+e3QoJ3Byb3BlcnR5LnRpdGxlJyl9ICo8L0xhYmVsPlxuICAgICAgICAgICAgPElucHV0XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT17YGZvcm0taW5wdXQgJHtlcnJvcnMudGl0bGUgPyAnZXJyb3InIDogJyd9YH1cbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLnRpdGxlfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCd0aXRsZScsIGUudGFyZ2V0LnZhbHVlKX1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9e3QoJ3Byb3BlcnR5LnRpdGxlLnBsYWNlaG9sZGVyJyl9XG4gICAgICAgICAgICAgIGRpcj1cInJ0bFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAge2Vycm9ycy50aXRsZSAmJiA8ZGl2IGNsYXNzTmFtZT1cImZvcm0tZXJyb3JcIj57ZXJyb3JzLnRpdGxlfTwvZGl2Pn1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPnt0KCdwcm9wZXJ0eS5wcmljZScpfSAqPC9MYWJlbD5cbiAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bmb3JtLWlucHV0ICR7ZXJyb3JzLnByaWNlID8gJ2Vycm9yJyA6ICcnfWB9XG4gICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEucHJpY2V9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ3ByaWNlJywgZS50YXJnZXQudmFsdWUgPyBOdW1iZXIoZS50YXJnZXQudmFsdWUpIDogJycpfVxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj17dCgncHJvcGVydHkucHJpY2UucGxhY2Vob2xkZXInKX1cbiAgICAgICAgICAgICAgZGlyPVwicnRsXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICB7ZXJyb3JzLnByaWNlICYmIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1lcnJvclwiPntlcnJvcnMucHJpY2V9PC9kaXY+fVxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cblxuICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cInByb3BlcnR5LWdyaWQgcHJvcGVydHktZ3JpZC0yXCI+XG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJmb3JtLWxhYmVsXCI+e3QoJ3Byb3BlcnR5LnR5cGUnKX0gKjwvTGFiZWw+XG4gICAgICAgICAgICA8U2VsZWN0IHZhbHVlPXtmb3JtRGF0YS50eXBlfSBvblZhbHVlQ2hhbmdlPXsodmFsdWUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCd0eXBlJywgdmFsdWUpfT5cbiAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPXtgZm9ybS1zZWxlY3QgJHtlcnJvcnMudHlwZSA/ICdlcnJvcicgOiAnJ31gfT5cbiAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgcGxhY2Vob2xkZXI9XCLYp9iu2KrYsSDZhtmI2Lkg2KfZhNi52YLYp9ixXCIgLz5cbiAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICB7cHJvcGVydHlUeXBlcy5tYXAoKHR5cGUpID0+IChcbiAgICAgICAgICAgICAgICAgIDxTZWxlY3RJdGVtIGtleT17dHlwZS52YWx1ZX0gdmFsdWU9e3R5cGUudmFsdWV9PlxuICAgICAgICAgICAgICAgICAgICB7dHlwZS5sYWJlbH1cbiAgICAgICAgICAgICAgICAgIDwvU2VsZWN0SXRlbT5cbiAgICAgICAgICAgICAgICApKX1cbiAgICAgICAgICAgICAgPC9TZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgPC9TZWxlY3Q+XG4gICAgICAgICAgICB7ZXJyb3JzLnR5cGUgJiYgPGRpdiBjbGFzc05hbWU9XCJmb3JtLWVycm9yXCI+e2Vycm9ycy50eXBlfTwvZGl2Pn1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPnt0KCdwcm9wZXJ0eS5zdGF0dXMnKX08L0xhYmVsPlxuICAgICAgICAgICAgPFNlbGVjdCB2YWx1ZT17Zm9ybURhdGEuc3RhdHVzfSBvblZhbHVlQ2hhbmdlPXsodmFsdWUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdzdGF0dXMnLCB2YWx1ZSl9PlxuICAgICAgICAgICAgICA8U2VsZWN0VHJpZ2dlciBjbGFzc05hbWU9XCJmb3JtLXNlbGVjdFwiPlxuICAgICAgICAgICAgICAgIDxTZWxlY3RWYWx1ZSAvPlxuICAgICAgICAgICAgICA8L1NlbGVjdFRyaWdnZXI+XG4gICAgICAgICAgICAgIDxTZWxlY3RDb250ZW50PlxuICAgICAgICAgICAgICAgIHtwcm9wZXJ0eVN0YXR1c2VzLm1hcCgoc3RhdHVzKSA9PiAoXG4gICAgICAgICAgICAgICAgICA8U2VsZWN0SXRlbSBrZXk9e3N0YXR1cy52YWx1ZX0gdmFsdWU9e3N0YXR1cy52YWx1ZX0+XG4gICAgICAgICAgICAgICAgICAgIHtzdGF0dXMubGFiZWx9XG4gICAgICAgICAgICAgICAgICA8L1NlbGVjdEl0ZW0+XG4gICAgICAgICAgICAgICAgKSl9XG4gICAgICAgICAgICAgIDwvU2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgIDwvU2VsZWN0PlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogUHJvcGVydHkgRGV0YWlscyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1zZWN0aW9uXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJhcmFiaWMtaGVhZGluZ1wiPtiq2YHYp9i12YrZhCDYp9mE2LnZgtin2LE8L2gzPlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHJvcGVydHktZ3JpZCBwcm9wZXJ0eS1ncmlkLTNcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cImZvcm0tbGFiZWxcIj57dCgncHJvcGVydHkuYmVkcm9vbXMnKX0gKjwvTGFiZWw+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZm9ybS1pbnB1dCAke2Vycm9ycy5iZWRyb29tcyA/ICdlcnJvcicgOiAnJ31gfVxuICAgICAgICAgICAgICB0eXBlPVwibnVtYmVyXCJcbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmJlZHJvb21zfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdiZWRyb29tcycsIGUudGFyZ2V0LnZhbHVlID8gTnVtYmVyKGUudGFyZ2V0LnZhbHVlKSA6ICcnKX1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIwXCJcbiAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgIGRpcj1cInJ0bFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAge2Vycm9ycy5iZWRyb29tcyAmJiA8ZGl2IGNsYXNzTmFtZT1cImZvcm0tZXJyb3JcIj57ZXJyb3JzLmJlZHJvb21zfTwvZGl2Pn1cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPnt0KCdwcm9wZXJ0eS5iYXRocm9vbXMnKX0gKjwvTGFiZWw+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZm9ybS1pbnB1dCAke2Vycm9ycy5iYXRocm9vbXMgPyAnZXJyb3InIDogJyd9YH1cbiAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5iYXRocm9vbXN9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ2JhdGhyb29tcycsIGUudGFyZ2V0LnZhbHVlID8gTnVtYmVyKGUudGFyZ2V0LnZhbHVlKSA6ICcnKX1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIwXCJcbiAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgIGRpcj1cInJ0bFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAge2Vycm9ycy5iYXRocm9vbXMgJiYgPGRpdiBjbGFzc05hbWU9XCJmb3JtLWVycm9yXCI+e2Vycm9ycy5iYXRocm9vbXN9PC9kaXY+fVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJmb3JtLWxhYmVsXCI+e3QoJ3Byb3BlcnR5LmFyZWEnKX0gKjwvTGFiZWw+XG4gICAgICAgICAgICA8SW5wdXRcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPXtgZm9ybS1pbnB1dCAke2Vycm9ycy5hcmVhID8gJ2Vycm9yJyA6ICcnfWB9XG4gICAgICAgICAgICAgIHR5cGU9XCJudW1iZXJcIlxuICAgICAgICAgICAgICB2YWx1ZT17Zm9ybURhdGEuYXJlYX1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgnYXJlYScsIGUudGFyZ2V0LnZhbHVlID8gTnVtYmVyKGUudGFyZ2V0LnZhbHVlKSA6ICcnKX1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIwXCJcbiAgICAgICAgICAgICAgbWluPVwiMFwiXG4gICAgICAgICAgICAgIGRpcj1cInJ0bFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAge2Vycm9ycy5hcmVhICYmIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1lcnJvclwiPntlcnJvcnMuYXJlYX08L2Rpdj59XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHJvcGVydHktZ3JpZCBwcm9wZXJ0eS1ncmlkLTJcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cImZvcm0tbGFiZWxcIj57dCgncHJvcGVydHkueWVhckJ1aWx0Jyl9PC9MYWJlbD5cbiAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb3JtLWlucHV0XCJcbiAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS55ZWFyQnVpbHR9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ3llYXJCdWlsdCcsIGUudGFyZ2V0LnZhbHVlID8gTnVtYmVyKGUudGFyZ2V0LnZhbHVlKSA6ICcnKX1cbiAgICAgICAgICAgICAgcGxhY2Vob2xkZXI9XCIyMDI0XCJcbiAgICAgICAgICAgICAgbWluPVwiMTkwMFwiXG4gICAgICAgICAgICAgIG1heD1cIjIwMzBcIlxuICAgICAgICAgICAgICBkaXI9XCJydGxcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICA8L2Rpdj5cblxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPnt0KCdwcm9wZXJ0eS5wYXJraW5nJyl9PC9MYWJlbD5cbiAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICBjbGFzc05hbWU9XCJmb3JtLWlucHV0XCJcbiAgICAgICAgICAgICAgdHlwZT1cIm51bWJlclwiXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5wYXJraW5nfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdwYXJraW5nJywgZS50YXJnZXQudmFsdWUgPyBOdW1iZXIoZS50YXJnZXQudmFsdWUpIDogJycpfVxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cIjBcIlxuICAgICAgICAgICAgICBtaW49XCIwXCJcbiAgICAgICAgICAgICAgZGlyPVwicnRsXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBMb2NhdGlvbiBJbmZvcm1hdGlvbiAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1zZWN0aW9uXCI+XG4gICAgICAgIDxoMyBjbGFzc05hbWU9XCJhcmFiaWMtaGVhZGluZ1wiPtmF2LnZhNmI2YXYp9iqINin2YTZhdmI2YLYuTwvaDM+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwcm9wZXJ0eS1ncmlkIHByb3BlcnR5LWdyaWQtMlwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPnt0KCdwcm9wZXJ0eS5sb2NhdGlvbicpfSAqPC9MYWJlbD5cbiAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bmb3JtLWlucHV0ICR7ZXJyb3JzLmxvY2F0aW9uID8gJ2Vycm9yJyA6ICcnfWB9XG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5sb2NhdGlvbn1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgnbG9jYXRpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXt0KCdwcm9wZXJ0eS5sb2NhdGlvbi5wbGFjZWhvbGRlcicpfVxuICAgICAgICAgICAgICBkaXI9XCJydGxcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIHtlcnJvcnMubG9jYXRpb24gJiYgPGRpdiBjbGFzc05hbWU9XCJmb3JtLWVycm9yXCI+e2Vycm9ycy5sb2NhdGlvbn08L2Rpdj59XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cImZvcm0tbGFiZWxcIj57dCgncHJvcGVydHkuYWRkcmVzcycpfSAqPC9MYWJlbD5cbiAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bmb3JtLWlucHV0ICR7ZXJyb3JzLmFkZHJlc3MgPyAnZXJyb3InIDogJyd9YH1cbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmFkZHJlc3N9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ2FkZHJlc3MnLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXt0KCdwcm9wZXJ0eS5hZGRyZXNzLnBsYWNlaG9sZGVyJyl9XG4gICAgICAgICAgICAgIGRpcj1cInJ0bFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAge2Vycm9ycy5hZGRyZXNzICYmIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1lcnJvclwiPntlcnJvcnMuYWRkcmVzc308L2Rpdj59XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHJvcGVydHktZ3JpZCBwcm9wZXJ0eS1ncmlkLTJcIj5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cImZvcm0tbGFiZWxcIj57dCgncHJvcGVydHkuY2l0eScpfSAqPC9MYWJlbD5cbiAgICAgICAgICAgIDxJbnB1dFxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bmb3JtLWlucHV0ICR7ZXJyb3JzLmNpdHkgPyAnZXJyb3InIDogJyd9YH1cbiAgICAgICAgICAgICAgdmFsdWU9e2Zvcm1EYXRhLmNpdHl9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ2NpdHknLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXt0KCdwcm9wZXJ0eS5jaXR5LnBsYWNlaG9sZGVyJyl9XG4gICAgICAgICAgICAgIGRpcj1cInJ0bFwiXG4gICAgICAgICAgICAvPlxuICAgICAgICAgICAge2Vycm9ycy5jaXR5ICYmIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1lcnJvclwiPntlcnJvcnMuY2l0eX08L2Rpdj59XG4gICAgICAgICAgPC9kaXY+XG5cbiAgICAgICAgICA8ZGl2PlxuICAgICAgICAgICAgPExhYmVsIGNsYXNzTmFtZT1cImZvcm0tbGFiZWxcIj57dCgncHJvcGVydHkuY291bnRyeScpfTwvTGFiZWw+XG4gICAgICAgICAgICA8U2VsZWN0IHZhbHVlPXtmb3JtRGF0YS5jb3VudHJ5fSBvblZhbHVlQ2hhbmdlPXsodmFsdWUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdjb3VudHJ5JywgdmFsdWUpfT5cbiAgICAgICAgICAgICAgPFNlbGVjdFRyaWdnZXIgY2xhc3NOYW1lPVwiZm9ybS1zZWxlY3RcIj5cbiAgICAgICAgICAgICAgICA8U2VsZWN0VmFsdWUgLz5cbiAgICAgICAgICAgICAgPC9TZWxlY3RUcmlnZ2VyPlxuICAgICAgICAgICAgICA8U2VsZWN0Q29udGVudD5cbiAgICAgICAgICAgICAgICB7Y291bnRyaWVzLm1hcCgoY291bnRyeSkgPT4gKFxuICAgICAgICAgICAgICAgICAgPFNlbGVjdEl0ZW0ga2V5PXtjb3VudHJ5LnZhbHVlfSB2YWx1ZT17Y291bnRyeS52YWx1ZX0+XG4gICAgICAgICAgICAgICAgICAgIHtjb3VudHJ5LmxhYmVsfVxuICAgICAgICAgICAgICAgICAgPC9TZWxlY3RJdGVtPlxuICAgICAgICAgICAgICAgICkpfVxuICAgICAgICAgICAgICA8L1NlbGVjdENvbnRlbnQ+XG4gICAgICAgICAgICA8L1NlbGVjdD5cbiAgICAgICAgICA8L2Rpdj5cbiAgICAgICAgPC9kaXY+XG4gICAgICA8L2Rpdj5cblxuICAgICAgey8qIERlc2NyaXB0aW9uICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb3JtLXNlY3Rpb25cIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT17bGFuZ3VhZ2UgPT09ICdhcicgPyAnYXJhYmljLWhlYWRpbmcnIDogJyd9PlxuICAgICAgICAgIHtsYW5ndWFnZSA9PT0gJ2FyJyA/ICfYp9mE2YjYtdmBJyA6ICdEZXNjcmlwdGlvbid9XG4gICAgICAgIDwvaDM+XG5cbiAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJwcm9wZXJ0eS1ncmlkIHByb3BlcnR5LWdyaWQtMlwiPlxuICAgICAgICAgIDxkaXY+XG4gICAgICAgICAgICA8TGFiZWwgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPnt0KCdwcm9wZXJ0eS5kZXNjcmlwdGlvbicpfSAqPC9MYWJlbD5cbiAgICAgICAgICAgIDxUZXh0YXJlYVxuICAgICAgICAgICAgICBjbGFzc05hbWU9e2Bmb3JtLXRleHRhcmVhICR7ZXJyb3JzLmRlc2NyaXB0aW9uID8gJ2Vycm9yJyA6ICcnfWB9XG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kZXNjcmlwdGlvbn1cbiAgICAgICAgICAgICAgb25DaGFuZ2U9eyhlKSA9PiBoYW5kbGVJbnB1dENoYW5nZSgnZGVzY3JpcHRpb24nLCBlLnRhcmdldC52YWx1ZSl9XG4gICAgICAgICAgICAgIHBsYWNlaG9sZGVyPXt0KCdwcm9wZXJ0eS5kZXNjcmlwdGlvbi5wbGFjZWhvbGRlcicpfVxuICAgICAgICAgICAgICBkaXI9e2xhbmd1YWdlID09PSAnYXInID8gJ3J0bCcgOiAnbHRyJ31cbiAgICAgICAgICAgICAgcm93cz17NH1cbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICB7ZXJyb3JzLmRlc2NyaXB0aW9uICYmIDxkaXYgY2xhc3NOYW1lPVwiZm9ybS1lcnJvclwiPntlcnJvcnMuZGVzY3JpcHRpb259PC9kaXY+fVxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdj5cbiAgICAgICAgICAgIDxMYWJlbCBjbGFzc05hbWU9XCJmb3JtLWxhYmVsXCI+e2xhbmd1YWdlID09PSAnYXInID8gJ9in2YTZiNi12YEg2KjYp9mE2LnYsdio2YrYqScgOiAnRGVzY3JpcHRpb24gKEFyYWJpYyknfTwvTGFiZWw+XG4gICAgICAgICAgICA8VGV4dGFyZWFcbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwiZm9ybS10ZXh0YXJlYVwiXG4gICAgICAgICAgICAgIHZhbHVlPXtmb3JtRGF0YS5kZXNjcmlwdGlvbkFyfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdkZXNjcmlwdGlvbkFyJywgZS50YXJnZXQudmFsdWUpfVxuICAgICAgICAgICAgICBwbGFjZWhvbGRlcj1cItij2K/YrtmEINmI2LXZgSDZhdmB2LXZhCDZhNmE2LnZgtin2LEg2KjYp9mE2LnYsdio2YrYqVwiXG4gICAgICAgICAgICAgIGRpcj1cInJ0bFwiXG4gICAgICAgICAgICAgIHJvd3M9ezR9XG4gICAgICAgICAgICAvPlxuICAgICAgICAgIDwvZGl2PlxuICAgICAgICA8L2Rpdj5cbiAgICAgIDwvZGl2PlxuXG4gICAgICB7LyogQWRkaXRpb25hbCBPcHRpb25zICovfVxuICAgICAgPGRpdiBjbGFzc05hbWU9XCJmb3JtLXNlY3Rpb25cIj5cbiAgICAgICAgPGgzIGNsYXNzTmFtZT17bGFuZ3VhZ2UgPT09ICdhcicgPyAnYXJhYmljLWhlYWRpbmcnIDogJyd9PlxuICAgICAgICAgIHtsYW5ndWFnZSA9PT0gJ2FyJyA/ICfYrtmK2KfYsdin2Kog2KXYttin2YHZitipJyA6ICdBZGRpdGlvbmFsIE9wdGlvbnMnfVxuICAgICAgICA8L2gzPlxuXG4gICAgICAgIDxkaXYgY2xhc3NOYW1lPVwicHJvcGVydHktZ3JpZCBwcm9wZXJ0eS1ncmlkLTJcIj5cbiAgICAgICAgICA8ZGl2IGNsYXNzTmFtZT1cImZsZXggaXRlbXMtY2VudGVyIHNwYWNlLXgtMlwiPlxuICAgICAgICAgICAgPGlucHV0XG4gICAgICAgICAgICAgIHR5cGU9XCJjaGVja2JveFwiXG4gICAgICAgICAgICAgIGlkPVwiZnVybmlzaGVkXCJcbiAgICAgICAgICAgICAgY2hlY2tlZD17Zm9ybURhdGEuZnVybmlzaGVkfVxuICAgICAgICAgICAgICBvbkNoYW5nZT17KGUpID0+IGhhbmRsZUlucHV0Q2hhbmdlKCdmdXJuaXNoZWQnLCBlLnRhcmdldC5jaGVja2VkKX1cbiAgICAgICAgICAgICAgY2xhc3NOYW1lPVwidy00IGgtNCB0ZXh0LWJsdWUtNjAwIGJnLWdyYXktMTAwIGJvcmRlci1ncmF5LTMwMCByb3VuZGVkIGZvY3VzOnJpbmctYmx1ZS01MDBcIlxuICAgICAgICAgICAgLz5cbiAgICAgICAgICAgIDxMYWJlbCBodG1sRm9yPVwiZnVybmlzaGVkXCIgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPlxuICAgICAgICAgICAgICB7dCgncHJvcGVydHkuZnVybmlzaGVkJyl9XG4gICAgICAgICAgICA8L0xhYmVsPlxuICAgICAgICAgIDwvZGl2PlxuXG4gICAgICAgICAgPGRpdiBjbGFzc05hbWU9XCJmbGV4IGl0ZW1zLWNlbnRlciBzcGFjZS14LTJcIj5cbiAgICAgICAgICAgIDxpbnB1dFxuICAgICAgICAgICAgICB0eXBlPVwiY2hlY2tib3hcIlxuICAgICAgICAgICAgICBpZD1cInBldEZyaWVuZGx5XCJcbiAgICAgICAgICAgICAgY2hlY2tlZD17Zm9ybURhdGEucGV0RnJpZW5kbHl9XG4gICAgICAgICAgICAgIG9uQ2hhbmdlPXsoZSkgPT4gaGFuZGxlSW5wdXRDaGFuZ2UoJ3BldEZyaWVuZGx5JywgZS50YXJnZXQuY2hlY2tlZCl9XG4gICAgICAgICAgICAgIGNsYXNzTmFtZT1cInctNCBoLTQgdGV4dC1ibHVlLTYwMCBiZy1ncmF5LTEwMCBib3JkZXItZ3JheS0zMDAgcm91bmRlZCBmb2N1czpyaW5nLWJsdWUtNTAwXCJcbiAgICAgICAgICAgIC8+XG4gICAgICAgICAgICA8TGFiZWwgaHRtbEZvcj1cInBldEZyaWVuZGx5XCIgY2xhc3NOYW1lPVwiZm9ybS1sYWJlbFwiPlxuICAgICAgICAgICAgICB7dCgncHJvcGVydHkucGV0RnJpZW5kbHknKX1cbiAgICAgICAgICAgIDwvTGFiZWw+XG4gICAgICAgICAgPC9kaXY+XG4gICAgICAgIDwvZGl2PlxuICAgICAgPC9kaXY+XG5cbiAgICAgIHsvKiBBY3Rpb24gQnV0dG9ucyAqL31cbiAgICAgIDxkaXYgY2xhc3NOYW1lPXtgZmxleCBnYXAtNCAke2xhbmd1YWdlID09PSAnYXInID8gJ2ZsZXgtcm93LXJldmVyc2UnIDogJyd9YH0+XG4gICAgICAgIDxCdXR0b25cbiAgICAgICAgICB0eXBlPVwic3VibWl0XCJcbiAgICAgICAgICBkaXNhYmxlZD17aXNMb2FkaW5nfVxuICAgICAgICAgIGNsYXNzTmFtZT1cImJ0bi1wcmltYXJ5XCJcbiAgICAgICAgPlxuICAgICAgICAgIHtpc0xvYWRpbmcgJiYgPGRpdiBjbGFzc05hbWU9XCJsb2FkaW5nLXNwaW5uZXJcIiAvPn1cbiAgICAgICAgICB7aXNMb2FkaW5nID8gdCgncHJvcGVydGllcy5sb2FkaW5nJykgOiB0KCdwcm9wZXJ0aWVzLnNhdmUnKX1cbiAgICAgICAgPC9CdXR0b24+XG5cbiAgICAgICAgPEJ1dHRvblxuICAgICAgICAgIHR5cGU9XCJidXR0b25cIlxuICAgICAgICAgIG9uQ2xpY2s9e29uQ2FuY2VsfVxuICAgICAgICAgIGRpc2FibGVkPXtpc0xvYWRpbmd9XG4gICAgICAgICAgY2xhc3NOYW1lPVwiYnRuLXNlY29uZGFyeVwiXG4gICAgICAgID5cbiAgICAgICAgICB7dCgncHJvcGVydGllcy5jYW5jZWwnKX1cbiAgICAgICAgPC9CdXR0b24+XG4gICAgICA8L2Rpdj5cbiAgICA8L2Zvcm0+XG4gIClcbn1cbiJdLCJuYW1lcyI6WyJ1c2VTdGF0ZSIsInVzZUNhbGxiYWNrIiwidXNlUmVmIiwidXNlU2ltcGxlTGFuZ3VhZ2UiLCJCdXR0b24iLCJJbnB1dCIsIkxhYmVsIiwiVGV4dGFyZWEiLCJTZWxlY3QiLCJTZWxlY3RDb250ZW50IiwiU2VsZWN0SXRlbSIsIlNlbGVjdFRyaWdnZXIiLCJTZWxlY3RWYWx1ZSIsInVzZVRvYXN0IiwicHJvcGVydHlTZXJ2aWNlIiwiUHJvcGVydHlDcmVhdGVGb3JtIiwib25TdWNjZXNzIiwib25DYW5jZWwiLCJpc0xvYWRpbmciLCJzZXRJc0xvYWRpbmciLCJ0IiwidG9hc3QiLCJmaWxlSW5wdXRSZWYiLCJmb3JtRGF0YSIsInNldEZvcm1EYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsInByaWNlIiwiY3VycmVuY3kiLCJ0eXBlIiwic3RhdHVzIiwiYmVkcm9vbXMiLCJiYXRocm9vbXMiLCJhcmVhIiwibG9jYXRpb24iLCJhZGRyZXNzIiwiY2l0eSIsImNvdW50cnkiLCJpbWFnZXMiLCJmZWF0dXJlcyIsImFtZW5pdGllcyIsInllYXJCdWlsdCIsInBhcmtpbmciLCJmdXJuaXNoZWQiLCJwZXRGcmllbmRseSIsImVycm9ycyIsInNldEVycm9ycyIsImltYWdlUHJldmlld1VybHMiLCJzZXRJbWFnZVByZXZpZXdVcmxzIiwidXBsb2FkaW5nSW1hZ2VzIiwic2V0VXBsb2FkaW5nSW1hZ2VzIiwiaGFuZGxlSW5wdXRDaGFuZ2UiLCJmaWVsZCIsInZhbHVlIiwicHJldiIsImhhbmRsZUltYWdlQ2hhbmdlIiwiZmlsZXMiLCJ2YWxpZEZpbGVzIiwibmV3UHJldmlld1VybHMiLCJBcnJheSIsImZyb20iLCJmb3JFYWNoIiwiZmlsZSIsInN0YXJ0c1dpdGgiLCJ2YXJpYW50Iiwic2l6ZSIsInB1c2giLCJVUkwiLCJjcmVhdGVPYmplY3RVUkwiLCJsZW5ndGgiLCJyZW1vdmVJbWFnZSIsImluZGV4IiwiZmlsdGVyIiwiXyIsImkiLCJyZXZva2VPYmplY3RVUkwiLCJoYW5kbGVEcmFnT3ZlciIsImUiLCJwcmV2ZW50RGVmYXVsdCIsInN0b3BQcm9wYWdhdGlvbiIsImhhbmRsZURyb3AiLCJkYXRhVHJhbnNmZXIiLCJ2YWxpZGF0ZUZvcm0iLCJuZXdFcnJvcnMiLCJ0cmltIiwiT2JqZWN0Iiwia2V5cyIsImhhbmRsZVN1Ym1pdCIsImltYWdlVXJscyIsImltYWdlRm9ybURhdGEiLCJGb3JtRGF0YSIsImFwcGVuZCIsIm1hcCIsIkRhdGUiLCJub3ciLCJuYW1lIiwicHJvcGVydHlEYXRhIiwiTnVtYmVyIiwidW5kZWZpbmVkIiwiY3JlYXRlUHJvcGVydHkiLCJlcnJvciIsImNvbnNvbGUiLCJwcm9wZXJ0eVR5cGVzIiwibGFiZWwiLCJwcm9wZXJ0eVN0YXR1c2VzIiwiY291bnRyaWVzIiwiZm9ybSIsIm9uU3VibWl0IiwiY2xhc3NOYW1lIiwiZGl2IiwiaDMiLCJvbkNoYW5nZSIsInRhcmdldCIsInBsYWNlaG9sZGVyIiwiZGlyIiwib25WYWx1ZUNoYW5nZSIsIm1pbiIsIm1heCIsImxhbmd1YWdlIiwicm93cyIsImRlc2NyaXB0aW9uQXIiLCJpbnB1dCIsImlkIiwiY2hlY2tlZCIsImh0bWxGb3IiLCJkaXNhYmxlZCIsIm9uQ2xpY2siXSwiaWdub3JlTGlzdCI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/properties/PropertyCreateForm.tsx\n"));

/***/ })

});