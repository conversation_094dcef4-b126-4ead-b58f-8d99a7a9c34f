"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./lib/uploadthing.ts":
/*!****************************!*\
  !*** ./lib/uploadthing.ts ***!
  \****************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   UploadButton: () => (/* binding */ UploadButton),\n/* harmony export */   UploadDropzone: () => (/* binding */ UploadDropzone),\n/* harmony export */   uploadFiles: () => (/* binding */ uploadFiles),\n/* harmony export */   useUploadThing: () => (/* binding */ useUploadThing)\n/* harmony export */ });\n/* harmony import */ var _uploadthing_react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! @uploadthing/react */ \"(app-pages-browser)/./node_modules/@uploadthing/react/dist/button-client-BLNyMUF0.js\");\n/* harmony import */ var _uploadthing_react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! @uploadthing/react */ \"(app-pages-browser)/./node_modules/@uploadthing/react/dist/index.js\");\n\n// Configure UploadThing to use the Express backend\nconst { useUploadThing, uploadFiles } = (0,_uploadthing_react__WEBPACK_IMPORTED_MODULE_0__.g)({\n    url: \"\".concat(\"http://localhost:5000\" || 0, \"/api/v1/uploadthing\")\n});\nconst UploadButton = (0,_uploadthing_react__WEBPACK_IMPORTED_MODULE_1__.generateUploadButton)({\n    url: \"\".concat(\"http://localhost:5000\" || 0, \"/api/v1/uploadthing\")\n});\nconst UploadDropzone = (0,_uploadthing_react__WEBPACK_IMPORTED_MODULE_1__.generateUploadDropzone)({\n    url: \"\".concat(\"http://localhost:5000\" || 0, \"/api/v1/uploadthing\")\n});\n\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/uploadthing.ts\n"));

/***/ })

});