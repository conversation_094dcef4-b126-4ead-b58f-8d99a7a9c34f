"use client"

import { useState, useEffect } from 'react'
import { useP<PERSON><PERSON>, useRouter } from 'next/navigation'
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage'
import { propertyService } from '@/services/propertyService'
import { PropertyShowComponent } from '@/components/properties/PropertyShowComponent'
import { Button } from '@/components/ui/button'
import { ArrowRight, Edit, Trash2 } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import '@/styles/arabic-properties.css'

interface Property {
  id: string
  title: string
  titleAr?: string
  description: string
  descriptionAr?: string
  price: number
  currency: string
  type: string
  status: string
  bedrooms?: number
  bathrooms?: number
  area?: number
  location: string
  locationAr?: string
  address: string
  addressAr?: string
  city: string
  cityAr?: string
  country: string
  countryAr?: string
  latitude?: number
  longitude?: number
  images: string[]
  features: string[]
  featuresAr: string[]
  amenities: string[]
  amenitiesAr: string[]
  yearBuilt?: number
  parking?: number
  furnished: boolean
  petFriendly: boolean
  utilities?: string
  utilitiesAr?: string
  contactInfo?: string
  agentId?: string
  isActive: boolean
  isFeatured: boolean
  viewCount: number
  createdAt: string
  updatedAt: string
  agent?: {
    id: string
    name: string
    email: string
  }
}

export default function PropertyShowPage() {
  const params = useParams()
  const router = useRouter()
  const { t } = useSimpleLanguage()
  const { toast } = useToast()

  const [property, setProperty] = useState<Property | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [isDeleting, setIsDeleting] = useState(false)

  const propertyId = params.id as string

  useEffect(() => {
    if (propertyId) {
      fetchProperty()
    }
  }, [propertyId])

  const fetchProperty = async () => {
    try {
      setIsLoading(true)
      const data = await propertyService.getPropertyById(propertyId)
      setProperty(data)
    } catch (error) {
      console.error('Error fetching property:', error)

      // Fallback to mock data if backend is not available
      console.log('Backend not available, using mock data for property:', propertyId)
      const mockProperty: Property = {
        id: propertyId,
        title: 'فيلا فاخرة في دبي مارينا',
        titleAr: 'فيلا فاخرة في دبي مارينا',
        description: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة',
        descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة رائعة على البحر وحديقة خاصة',
        price: 2500000,
        currency: 'AED',
        type: 'VILLA',
        status: 'AVAILABLE',
        bedrooms: 4,
        bathrooms: 3,
        area: 350,
        location: 'دبي مارينا',
        locationAr: 'دبي مارينا',
        address: '123 ممشى المارينا',
        addressAr: '123 ممشى المارينا',
        city: 'دبي',
        cityAr: 'دبي',
        country: 'الإمارات العربية المتحدة',
        countryAr: 'الإمارات العربية المتحدة',
        latitude: 25.0772,
        longitude: 55.1395,
        images: [
          'https://images.unsplash.com/photo-1613490493576-7fde63acd811?w=800&h=600&fit=crop',
          'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800&h=600&fit=crop',
          'https://images.unsplash.com/photo-1570129477492-45c003edd2be?w=800&h=600&fit=crop',
          'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800&h=600&fit=crop',
          'https://images.unsplash.com/photo-1582268611958-ebfd161ef9cf?w=800&h=600&fit=crop'
        ],
        features: ['مسبح خاص', 'حديقة', 'موقف سيارات', 'أمن 24/7'],
        featuresAr: ['مسبح خاص', 'حديقة', 'موقف سيارات', 'أمن 24/7'],
        amenities: ['صالة رياضية', 'سبا', 'ملعب تنس', 'مارينا خاصة'],
        amenitiesAr: ['صالة رياضية', 'سبا', 'ملعب تنس', 'مارينا خاصة'],
        yearBuilt: 2020,
        parking: 2,
        furnished: true,
        petFriendly: false,
        utilities: 'جميع المرافق متضمنة',
        utilitiesAr: 'جميع المرافق متضمنة',
        contactInfo: '+971 50 123 4567',
        agentId: 'agent1',
        isActive: true,
        isFeatured: true,
        viewCount: 125,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
        agent: {
          id: 'agent1',
          name: 'أحمد محمد',
          email: '<EMAIL>'
        }
      }

      setProperty(mockProperty)
    } finally {
      setIsLoading(false)
    }
  }

  const handleEdit = () => {
    router.push(`/dashboard/properties/${propertyId}/edit`)
  }

  const handleDelete = async () => {
    if (!confirm('هل أنت متأكد من حذف هذا العقار؟')) {
      return
    }

    try {
      setIsDeleting(true)
      await propertyService.deleteProperty(propertyId)
      toast({
        title: 'تم حذف العقار',
        description: 'تم حذف العقار بنجاح',
      })
      router.push('/dashboard/properties')
    } catch (error) {
      console.error('Error deleting property:', error)
      toast({
        title: 'خطأ في حذف العقار',
        description: 'حدث خطأ أثناء حذف العقار',
        variant: 'destructive',
      })
    } finally {
      setIsDeleting(false)
    }
  }

  const handleBack = () => {
    router.push('/dashboard/properties')
  }

  if (isLoading) {
    return (
      <div className="min-h-screen property-form-dark rtl arabic-text">
        <div className="max-w-7xl mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="loading-spinner" />
            <span className="mr-3 text-lg">جاري تحميل العقار...</span>
          </div>
        </div>
      </div>
    )
  }

  if (!property) {
    return (
      <div className="min-h-screen property-form-dark rtl arabic-text">
        <div className="max-w-7xl mx-auto p-6">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-red-400 mb-4">العقار غير موجود</h1>
            <p className="text-gray-400 mb-6">لم يتم العثور على العقار المطلوب</p>
            <Button onClick={handleBack} className="btn-primary">
              <ArrowRight className="h-4 w-4 ml-2" />
              العودة إلى قائمة العقارات
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen property-form-dark rtl arabic-text">
      <div className="max-w-7xl mx-auto p-6">
        {/* Header */}
        <div className="property-header-dark mb-6">
          <div className="flex items-center justify-between">
            <div>
              <Button
                onClick={handleBack}
                variant="ghost"
                className="text-gray-400 hover:text-white mb-4"
              >
                <ArrowRight className="h-4 w-4 ml-2" />
                العودة إلى قائمة العقارات
              </Button>
              <h1 className="arabic-heading text-3xl font-bold text-white">
                {property.title}
              </h1>
              <p className="text-gray-400 mt-2">
                {property.location} • {property.city} • {property.country}
              </p>
            </div>

            <div className="flex gap-3">
              <Button
                onClick={handleEdit}
                className="btn-secondary"
              >
                <Edit className="h-4 w-4 ml-2" />
                تعديل
              </Button>
              <Button
                onClick={handleDelete}
                disabled={isDeleting}
                className="bg-red-600 hover:bg-red-700 text-white"
              >
                {isDeleting && <div className="loading-spinner" />}
                <Trash2 className="h-4 w-4 ml-2" />
                حذف
              </Button>
            </div>
          </div>
        </div>

        {/* Property Details Component */}
        <PropertyShowComponent property={property} />
      </div>
    </div>
  )
}
