"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/create/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/create/page.tsx":
/*!**************************************************!*\
  !*** ./app/dashboard/properties/create/page.tsx ***!
  \**************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ CreatePropertyPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _components_properties_PropertyCreateForm__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/properties/PropertyCreateForm */ \"(app-pages-browser)/./components/properties/PropertyCreateForm.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _styles_arabic_properties_css__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/styles/arabic-properties.css */ \"(app-pages-browser)/./styles/arabic-properties.css\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\nfunction CreatePropertyPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_4__.useSimpleLanguage)();\n    const [isLoading, setIsLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const handleSuccess = ()=>{\n        router.push('/dashboard/properties');\n    };\n    const handleCancel = ()=>{\n        router.push('/dashboard/properties');\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen property-form-dark rtl arabic-text\",\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n            className: \"max-w-4xl mx-auto p-6\",\n            children: [\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"property-header-dark\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                            className: \"arabic-heading\",\n                            children: t('properties.create')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 27,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                            className: \"text-slate-400\",\n                            children: t('properties.subtitle')\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                            lineNumber: 30,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                    lineNumber: 26,\n                    columnNumber: 9\n                }, this),\n                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_properties_PropertyCreateForm__WEBPACK_IMPORTED_MODULE_3__.PropertyCreateForm, {\n                    onSuccess: handleSuccess,\n                    onCancel: handleCancel,\n                    isLoading: isLoading,\n                    setIsLoading: setIsLoading\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n                    lineNumber: 36,\n                    columnNumber: 9\n                }, this)\n            ]\n        }, void 0, true, {\n            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n            lineNumber: 24,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\create\\\\page.tsx\",\n        lineNumber: 23,\n        columnNumber: 5\n    }, this);\n}\n_s(CreatePropertyPage, \"GIbGm34rTsfZIGI58OLkma5CLto=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_4__.useSimpleLanguage\n    ];\n});\n_c = CreatePropertyPage;\nvar _c;\n$RefreshReg$(_c, \"CreatePropertyPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/create/page.tsx\n"));

/***/ })

});