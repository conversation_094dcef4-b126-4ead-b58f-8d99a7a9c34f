import api from '@/lib/api';

export interface PropertyCreateData {
  title: string;
  description: string;
  price: number;
  currency?: string;
  type: string;
  status?: string;
  bedrooms: number;
  bathrooms: number;
  area: number;
  location: string;
  address: string;
  city: string;
  country?: string;
  latitude?: number;
  longitude?: number;
  images?: string[];
  features?: string[];
  amenities?: string[];
  yearBuilt?: number;
  parking?: number;
  furnished?: boolean;
  petFriendly?: boolean;
  utilities?: string;
  contactInfo?: string;
  agentId?: string;
  isActive?: boolean;
  isFeatured?: boolean;
}

export interface PropertyResponse {
  id: string;
  title: string;
  titleAr?: string;
  description: string;
  descriptionAr?: string;
  price: number;
  currency: string;
  type: string;
  status: string;
  bedrooms?: number;
  bathrooms?: number;
  area?: number;
  location: string;
  locationAr?: string;
  address: string;
  addressAr?: string;
  city: string;
  cityAr?: string;
  country: string;
  countryAr?: string;
  latitude?: number;
  longitude?: number;
  images: string[];
  features: string[];
  featuresAr: string[];
  amenities: string[];
  amenitiesAr: string[];
  yearBuilt?: number;
  parking?: number;
  furnished: boolean;
  petFriendly: boolean;
  utilities?: string;
  utilitiesAr?: string;
  contactInfo?: string;
  agentId?: string;
  isActive: boolean;
  isFeatured: boolean;
  viewCount: number;
  createdAt: string;
  updatedAt: string;
  agent?: {
    id: string;
    name: string;
    email: string;
  };
}

/**
 * Creates a new property by sending data to the API
 * @param propertyData The property data to send
 * @returns The created property data
 */
export const createProperty = async (propertyData: PropertyCreateData): Promise<PropertyResponse> => {
  try {
    // Clean the data before sending
    const cleanedData = {
      ...propertyData,
      // Ensure arrays are properly initialized
      images: propertyData.images || [],
      features: propertyData.features || [],
      featuresAr: propertyData.featuresAr || [],
      amenities: propertyData.amenities || [],
      amenitiesAr: propertyData.amenitiesAr || [],
      // Set defaults for optional fields
      currency: propertyData.currency || 'USD',
      country: propertyData.country || 'UAE',
      status: propertyData.status || 'AVAILABLE',
      isActive: propertyData.isActive !== undefined ? propertyData.isActive : true,
      isFeatured: propertyData.isFeatured || false,
    };

    // Send as JSON using our API client
    const response = await api.post('/properties', cleanedData);
    return response.data || response;
  } catch (error) {
    console.error('Error creating property:', error);
    throw error;
  }
};

/**
 * Fetches all properties from the API
 * @returns List of properties
 */
export const getProperties = async (): Promise<PropertyResponse[]> => {
  try {
    return await api.get('/properties');
  } catch (error) {
    console.error('Error fetching properties:', error);
    throw error;
  }
};

/**
 * Fetches a single property by ID
 * @param id The property ID
 * @returns The property data
 */
export const getPropertyById = async (id: string): Promise<PropertyResponse> => {
  try {
    return await api.get(`/properties/${id}`);
  } catch (error) {
    console.error(`Error fetching property with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Updates an existing property
 * @param id The property ID
 * @param propertyData The updated property data
 * @returns The updated property data
 */
export const updateProperty = async (id: string, propertyData: Partial<PropertyCreateData>): Promise<PropertyResponse> => {
  try {
    // If there are images, we need to use FormData
    if (propertyData.images && propertyData.images.length > 0) {
      const formData = new FormData();

      // Add all property data to the form
      Object.entries(propertyData).forEach(([key, value]) => {
        if (key !== 'images') {
          formData.append(key, String(value));
        }
      });

      // Add images to the form
      Array.from(propertyData.images).forEach((image, index) => {
        formData.append(`images`, image);
      });

      // Send the request to the API using our API client
      return await api.upload(`/properties/${id}`, formData);
    } else {
      // If no images, send as JSON using our API client
      return await api.put(`/properties/${id}`, propertyData);
    }
  } catch (error) {
    console.error(`Error updating property with ID ${id}:`, error);
    throw error;
  }
};

/**
 * Deletes a property
 * @param id The property ID
 */
export const deleteProperty = async (id: string): Promise<void> => {
  try {
    await api.delete(`/properties/${id}`);
  } catch (error) {
    console.error(`Error deleting property with ID ${id}:`, error);
    throw error;
  }
};

// Default export for the property service
export const propertyService = {
  createProperty,
  getProperties,
  getPropertyById,
  updateProperty,
  deleteProperty,
};

export default propertyService;
