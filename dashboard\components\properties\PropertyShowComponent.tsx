"use client"

import { useState } from 'react'
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  MapPin,
  Bed,
  Bath,
  Square,
  Calendar,
  Car,
  Home,
  Heart,
  Eye,
  Phone,
  Mail,
  ChevronLeft,
  ChevronRight,
  X
} from 'lucide-react'
import '@/styles/arabic-properties.css'

interface Property {
  id: string
  title: string
  titleAr?: string
  description: string
  descriptionAr?: string
  price: number
  currency: string
  type: string
  status: string
  bedrooms?: number
  bathrooms?: number
  area?: number
  location: string
  locationAr?: string
  address: string
  addressAr?: string
  city: string
  cityAr?: string
  country: string
  countryAr?: string
  images: string[]
  features: string[]
  featuresAr: string[]
  amenities: string[]
  amenitiesAr: string[]
  yearBuilt?: number
  parking?: number
  furnished: boolean
  petFriendly: boolean
  utilities?: string
  utilitiesAr?: string
  contactInfo?: string
  agentId?: string
  isActive: boolean
  isFeatured: boolean
  viewCount: number
  createdAt: string
  updatedAt: string
  agent?: {
    id: string
    name: string
    email: string
  }
}

interface PropertyShowComponentProps {
  property: Property
}

export function PropertyShowComponent({ property }: PropertyShowComponentProps) {
  const { t } = useSimpleLanguage()
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isImageModalOpen, setIsImageModalOpen] = useState(false)

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('ar-AE', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(price)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return 'bg-green-600'
      case 'SOLD': return 'bg-red-600'
      case 'RENTED': return 'bg-blue-600'
      case 'PENDING': return 'bg-yellow-600'
      default: return 'bg-gray-600'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return 'متاح'
      case 'SOLD': return 'مباع'
      case 'RENTED': return 'مؤجر'
      case 'PENDING': return 'قيد المراجعة'
      default: return status
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case 'APARTMENT': return 'شقة'
      case 'VILLA': return 'فيلا'
      case 'TOWNHOUSE': return 'تاون هاوس'
      case 'PENTHOUSE': return 'بنتهاوس'
      case 'STUDIO': return 'استوديو'
      case 'OFFICE': return 'مكتب'
      case 'SHOP': return 'محل تجاري'
      case 'WAREHOUSE': return 'مستودع'
      case 'LAND': return 'أرض'
      case 'BUILDING': return 'مبنى'
      default: return type
    }
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) =>
      prev === property.images.length - 1 ? 0 : prev + 1
    )
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) =>
      prev === 0 ? property.images.length - 1 : prev - 1
    )
  }

  const openImageModal = (index: number) => {
    setCurrentImageIndex(index)
    setIsImageModalOpen(true)
  }

  return (
    <div className="space-y-6">
      {/* Image Gallery */}
      {property.images && property.images.length > 0 && (
        <Card className="property-card-dark">
          <CardContent className="p-0">
            <div className="relative">
              <div className="aspect-video bg-gray-800 rounded-lg overflow-hidden">
                <img
                  src={property.images[currentImageIndex]}
                  alt={property.title}
                  className="w-full h-full object-cover cursor-pointer"
                  onClick={() => openImageModal(currentImageIndex)}
                />
              </div>

              {property.images.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2"
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-2"
                  >
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </>
              )}

              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                {property.images.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`w-3 h-3 rounded-full ${
                      index === currentImageIndex ? 'bg-white' : 'bg-white/50'
                    }`}
                  />
                ))}
              </div>
            </div>

            {/* Thumbnail Gallery */}
            {property.images.length > 1 && (
              <div className="p-4">
                <div className="grid grid-cols-6 gap-2">
                  {property.images.slice(0, 6).map((image, index) => (
                    <div
                      key={index}
                      className={`aspect-square bg-gray-800 rounded-lg overflow-hidden cursor-pointer border-2 ${
                        index === currentImageIndex ? 'border-blue-500' : 'border-transparent'
                      }`}
                      onClick={() => setCurrentImageIndex(index)}
                    >
                      <img
                        src={image}
                        alt={`${property.title} ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Property Info Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <Card className="property-card-dark">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="arabic-heading text-xl">معلومات العقار</CardTitle>
                <div className="flex gap-2">
                  <Badge className={getStatusColor(property.status)}>
                    {getStatusText(property.status)}
                  </Badge>
                  {property.isFeatured && (
                    <Badge className="bg-yellow-600">مميز</Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="flex items-center gap-2 text-gray-300">
                  <Home className="h-5 w-5 text-blue-400" />
                  <span>{getTypeText(property.type)}</span>
                </div>

                {property.bedrooms && (
                  <div className="flex items-center gap-2 text-gray-300">
                    <Bed className="h-5 w-5 text-blue-400" />
                    <span>{property.bedrooms} غرف نوم</span>
                  </div>
                )}

                {property.bathrooms && (
                  <div className="flex items-center gap-2 text-gray-300">
                    <Bath className="h-5 w-5 text-blue-400" />
                    <span>{property.bathrooms} حمامات</span>
                  </div>
                )}

                {property.area && (
                  <div className="flex items-center gap-2 text-gray-300">
                    <Square className="h-5 w-5 text-blue-400" />
                    <span>{property.area} م²</span>
                  </div>
                )}
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                {property.yearBuilt && (
                  <div className="flex items-center gap-2 text-gray-300">
                    <Calendar className="h-5 w-5 text-blue-400" />
                    <span>سنة البناء: {property.yearBuilt}</span>
                  </div>
                )}

                {property.parking && (
                  <div className="flex items-center gap-2 text-gray-300">
                    <Car className="h-5 w-5 text-blue-400" />
                    <span>{property.parking} مواقف سيارات</span>
                  </div>
                )}
              </div>

              <div className="flex gap-4">
                {property.furnished && (
                  <Badge variant="outline" className="text-green-400 border-green-400">
                    مفروش
                  </Badge>
                )}
                {property.petFriendly && (
                  <Badge variant="outline" className="text-green-400 border-green-400">
                    يسمح بالحيوانات الأليفة
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Description */}
          <Card className="property-card-dark">
            <CardHeader>
              <CardTitle className="arabic-heading">الوصف</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 leading-relaxed whitespace-pre-wrap">
                {property.description}
              </p>
            </CardContent>
          </Card>

          {/* Features */}
          {property.features && property.features.length > 0 && (
            <Card className="property-card-dark">
              <CardHeader>
                <CardTitle className="arabic-heading">المميزات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {property.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-gray-300">
                      <div className="w-2 h-2 bg-blue-400 rounded-full" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Amenities */}
          {property.amenities && property.amenities.length > 0 && (
            <Card className="property-card-dark">
              <CardHeader>
                <CardTitle className="arabic-heading">الخدمات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {property.amenities.map((amenity, index) => (
                    <div key={index} className="flex items-center gap-2 text-gray-300">
                      <div className="w-2 h-2 bg-green-400 rounded-full" />
                      <span>{amenity}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Utilities */}
          {property.utilities && (
            <Card className="property-card-dark">
              <CardHeader>
                <CardTitle className="arabic-heading">المرافق المشمولة</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300">{property.utilities}</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Price */}
          <Card className="property-card-dark">
            <CardContent className="p-6">
              <div className="text-center">
                <div className="text-3xl font-bold text-white mb-2">
                  {formatPrice(property.price, property.currency)}
                </div>
                <div className="flex items-center justify-center gap-2 text-gray-400">
                  <Eye className="h-4 w-4" />
                  <span>{property.viewCount} مشاهدة</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location */}
          <Card className="property-card-dark">
            <CardHeader>
              <CardTitle className="arabic-heading flex items-center gap-2">
                <MapPin className="h-5 w-5" />
                الموقع
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-2">
              <p className="text-gray-300">{property.address}</p>
              <p className="text-gray-400">{property.city}, {property.country}</p>
            </CardContent>
          </Card>

          {/* Agent Info */}
          {property.agent && (
            <Card className="property-card-dark">
              <CardHeader>
                <CardTitle className="arabic-heading">معلومات الوكيل</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="font-medium text-white">{property.agent.name}</p>
                  <p className="text-gray-400 text-sm">{property.agent.email}</p>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" className="btn-primary flex-1">
                    <Phone className="h-4 w-4 ml-2" />
                    اتصال
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    <Mail className="h-4 w-4 ml-2" />
                    رسالة
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Image Modal */}
      {isImageModalOpen && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setIsImageModalOpen(false)}
              className="absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 z-10"
            >
              <X className="h-6 w-6" />
            </button>

            <img
              src={property.images[currentImageIndex]}
              alt={property.title}
              className="max-w-full max-h-full object-contain"
            />

            {property.images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
                >
                  <ChevronLeft className="h-6 w-6" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
                >
                  <ChevronRight className="h-6 w-6" />
                </button>
              </>
            )}

            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 rounded-full px-4 py-2">
              <span className="text-white text-sm">
                {currentImageIndex + 1} من {property.images.length}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
