"use client"

import { useState } from 'react'
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import {
  MapPin,
  Bed,
  Bath,
  Square,
  Calendar,
  Car,
  Home,
  Heart,
  Eye,
  Phone,
  Mail,
  ChevronLeft,
  ChevronRight,
  X
} from 'lucide-react'
import '@/styles/arabic-properties.css'

interface Property {
  id: string
  title: string
  titleAr?: string
  description: string
  descriptionAr?: string
  price: number
  currency: string
  type: string
  status: string
  bedrooms?: number
  bathrooms?: number
  area?: number
  location: string
  locationAr?: string
  address: string
  addressAr?: string
  city: string
  cityAr?: string
  country: string
  countryAr?: string
  images: string[]
  features: string[]
  featuresAr: string[]
  amenities: string[]
  amenitiesAr: string[]
  yearBuilt?: number
  parking?: number
  furnished: boolean
  petFriendly: boolean
  utilities?: string
  utilitiesAr?: string
  contactInfo?: string
  agentId?: string
  isActive: boolean
  isFeatured: boolean
  viewCount: number
  createdAt: string
  updatedAt: string
  agent?: {
    id: string
    name: string
    email: string
  }
}

interface PropertyShowComponentProps {
  property: Property
}

export function PropertyShowComponent({ property }: PropertyShowComponentProps) {
  const { t } = useSimpleLanguage()
  const [currentImageIndex, setCurrentImageIndex] = useState(0)
  const [isImageModalOpen, setIsImageModalOpen] = useState(false)

  const formatPrice = (price: number, currency: string) => {
    return new Intl.NumberFormat('ar-AE', {
      style: 'currency',
      currency: currency,
      minimumFractionDigits: 0,
    }).format(price)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return 'bg-green-600 text-white'
      case 'SOLD': return 'bg-red-600 text-white'
      case 'RENTED': return 'bg-blue-600 text-white'
      case 'PENDING': return 'bg-yellow-600 text-black'
      default: return 'bg-gray-600 text-white'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'AVAILABLE': return 'متاح'
      case 'SOLD': return 'مباع'
      case 'RENTED': return 'مؤجر'
      case 'PENDING': return 'قيد المراجعة'
      default: return status
    }
  }

  const getTypeText = (type: string) => {
    switch (type) {
      case 'APARTMENT': return 'شقة'
      case 'VILLA': return 'فيلا'
      case 'TOWNHOUSE': return 'تاون هاوس'
      case 'PENTHOUSE': return 'بنتهاوس'
      case 'STUDIO': return 'استوديو'
      case 'OFFICE': return 'مكتب'
      case 'SHOP': return 'محل تجاري'
      case 'WAREHOUSE': return 'مستودع'
      case 'LAND': return 'أرض'
      case 'BUILDING': return 'مبنى'
      default: return type
    }
  }

  const nextImage = () => {
    setCurrentImageIndex((prev) =>
      prev === property.images.length - 1 ? 0 : prev + 1
    )
  }

  const prevImage = () => {
    setCurrentImageIndex((prev) =>
      prev === 0 ? property.images.length - 1 : prev - 1
    )
  }

  const openImageModal = (index: number) => {
    setCurrentImageIndex(index)
    setIsImageModalOpen(true)
  }

  return (
    <div className="space-y-6">
      {/* Image Gallery */}
      {property.images && property.images.length > 0 && (
        <Card className="property-card-dark overflow-hidden">
          <CardContent className="p-0">
            <div className="relative group">
              <div className="aspect-video bg-gray-800 overflow-hidden relative">
                <img
                  src={property.images[currentImageIndex]}
                  alt={property.title}
                  className="w-full h-full object-cover cursor-pointer transition-transform duration-300 hover:scale-105"
                  onClick={() => openImageModal(currentImageIndex)}
                />

                {/* Image overlay with info */}
                <div className="absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />

                {/* Click to expand hint */}
                <div className="absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300">
                  انقر للتكبير
                </div>
              </div>

              {property.images.length > 1 && (
                <>
                  <button
                    onClick={prevImage}
                    className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white rounded-full p-3 transition-all duration-200 opacity-0 group-hover:opacity-100"
                  >
                    <ChevronLeft className="h-5 w-5" />
                  </button>
                  <button
                    onClick={nextImage}
                    className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white rounded-full p-3 transition-all duration-200 opacity-0 group-hover:opacity-100"
                  >
                    <ChevronRight className="h-5 w-5" />
                  </button>
                </>
              )}

              {/* Image counter */}
              <div className="absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm">
                {currentImageIndex + 1} / {property.images.length}
              </div>

              {/* Dot indicators */}
              <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2">
                {property.images.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => setCurrentImageIndex(index)}
                    className={`w-3 h-3 rounded-full transition-all duration-200 ${
                      index === currentImageIndex
                        ? 'bg-white scale-110'
                        : 'bg-white/50 hover:bg-white/70'
                    }`}
                  />
                ))}
              </div>
            </div>

            {/* Thumbnail Gallery */}
            {property.images.length > 1 && (
              <div className="p-4 bg-gray-900/50">
                <div className="grid grid-cols-6 gap-3">
                  {property.images.slice(0, 6).map((image, index) => (
                    <div
                      key={index}
                      className={`aspect-square bg-gray-800 rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-200 hover:scale-105 ${
                        index === currentImageIndex
                          ? 'border-blue-500 shadow-lg shadow-blue-500/25'
                          : 'border-transparent hover:border-gray-600'
                      }`}
                      onClick={() => setCurrentImageIndex(index)}
                    >
                      <img
                        src={image}
                        alt={`${property.title} ${index + 1}`}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  ))}
                  {property.images.length > 6 && (
                    <div className="aspect-square bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 text-sm border-2 border-dashed border-gray-600">
                      +{property.images.length - 6}
                    </div>
                  )}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      )}

      {/* Property Info Grid */}
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Info */}
        <div className="lg:col-span-2 space-y-6">
          {/* Basic Info */}
          <Card className="property-card-dark border-l-4 border-l-blue-500">
            <CardHeader className="pb-4">
              <div className="flex items-center justify-between">
                <CardTitle className="arabic-heading text-xl flex items-center gap-2">
                  <Home className="h-6 w-6 text-blue-400" />
                  معلومات العقار
                </CardTitle>
                <div className="flex gap-2">
                  <Badge className={getStatusColor(property.status)}>
                    {getStatusText(property.status)}
                  </Badge>
                  {property.isFeatured && (
                    <Badge className="bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-medium">
                      ⭐ مميز
                    </Badge>
                  )}
                </div>
              </div>
            </CardHeader>
            <CardContent className="space-y-6">
              {/* Property Type */}
              <div className="bg-gray-800/50 rounded-lg p-4">
                <div className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center">
                    <Home className="h-6 w-6 text-blue-400" />
                  </div>
                  <div>
                    <p className="text-sm text-gray-400">نوع العقار</p>
                    <p className="text-lg font-semibold text-white">{getTypeText(property.type)}</p>
                  </div>
                </div>
              </div>

              {/* Property Details Grid */}
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                {property.bedrooms && (
                  <div className="bg-gray-800/30 rounded-lg p-4 text-center">
                    <Bed className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-white">{property.bedrooms}</p>
                    <p className="text-sm text-gray-400">غرف نوم</p>
                  </div>
                )}

                {property.bathrooms && (
                  <div className="bg-gray-800/30 rounded-lg p-4 text-center">
                    <Bath className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-white">{property.bathrooms}</p>
                    <p className="text-sm text-gray-400">حمامات</p>
                  </div>
                )}

                {property.area && (
                  <div className="bg-gray-800/30 rounded-lg p-4 text-center">
                    <Square className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-white">{property.area}</p>
                    <p className="text-sm text-gray-400">متر مربع</p>
                  </div>
                )}

                {property.parking && (
                  <div className="bg-gray-800/30 rounded-lg p-4 text-center">
                    <Car className="h-8 w-8 text-blue-400 mx-auto mb-2" />
                    <p className="text-2xl font-bold text-white">{property.parking}</p>
                    <p className="text-sm text-gray-400">مواقف سيارات</p>
                  </div>
                )}
              </div>

              {/* Additional Info */}
              {property.yearBuilt && (
                <div className="bg-gray-800/30 rounded-lg p-4">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-5 w-5 text-blue-400" />
                    <span className="text-gray-300">سنة البناء: </span>
                    <span className="font-semibold text-white">{property.yearBuilt}</span>
                  </div>
                </div>
              )}

              {/* Property Features */}
              <div className="flex flex-wrap gap-3">
                {property.furnished && (
                  <Badge variant="outline" className="text-green-400 border-green-400 bg-green-400/10 px-3 py-1">
                    ✓ مفروش
                  </Badge>
                )}
                {property.petFriendly && (
                  <Badge variant="outline" className="text-green-400 border-green-400 bg-green-400/10 px-3 py-1">
                    ✓ يسمح بالحيوانات الأليفة
                  </Badge>
                )}
                {property.status === 'SOLD' && (
                  <Badge className="bg-red-600 text-white px-3 py-1">
                    🏷️ مباع
                  </Badge>
                )}
              </div>
            </CardContent>
          </Card>

          {/* Description */}
          <Card className="property-card-dark">
            <CardHeader>
              <CardTitle className="arabic-heading">الوصف</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-gray-300 leading-relaxed whitespace-pre-wrap">
                {property.description}
              </p>
            </CardContent>
          </Card>

          {/* Features */}
          {property.features && property.features.length > 0 && (
            <Card className="property-card-dark">
              <CardHeader>
                <CardTitle className="arabic-heading">المميزات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {property.features.map((feature, index) => (
                    <div key={index} className="flex items-center gap-2 text-gray-300">
                      <div className="w-2 h-2 bg-blue-400 rounded-full" />
                      <span>{feature}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Amenities */}
          {property.amenities && property.amenities.length > 0 && (
            <Card className="property-card-dark">
              <CardHeader>
                <CardTitle className="arabic-heading">الخدمات</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                  {property.amenities.map((amenity, index) => (
                    <div key={index} className="flex items-center gap-2 text-gray-300">
                      <div className="w-2 h-2 bg-green-400 rounded-full" />
                      <span>{amenity}</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Utilities */}
          {property.utilities && (
            <Card className="property-card-dark">
              <CardHeader>
                <CardTitle className="arabic-heading">المرافق المشمولة</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-gray-300">{property.utilities}</p>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Sidebar */}
        <div className="space-y-6">
          {/* Price Card */}
          <Card className="property-card-dark border-t-4 border-t-green-500">
            <CardContent className="p-6">
              <div className="text-center space-y-4">
                <div>
                  <p className="text-sm text-gray-400 mb-1">السعر</p>
                  <div className="text-4xl font-bold text-white mb-2">
                    {formatPrice(property.price, property.currency)}
                  </div>
                  {property.status === 'SOLD' && (
                    <div className="inline-flex items-center gap-2 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium">
                      🏷️ تم البيع
                    </div>
                  )}
                </div>

                <div className="flex items-center justify-center gap-4 pt-4 border-t border-gray-700">
                  <div className="flex items-center gap-2 text-gray-400">
                    <Eye className="h-4 w-4" />
                    <span>{property.viewCount} مشاهدة</span>
                  </div>
                  {property.isFeatured && (
                    <div className="flex items-center gap-2 text-yellow-400">
                      <Heart className="h-4 w-4" />
                      <span>مميز</span>
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Location Card */}
          <Card className="property-card-dark border-l-4 border-l-orange-500">
            <CardHeader className="pb-3">
              <CardTitle className="arabic-heading flex items-center gap-2 text-lg">
                <MapPin className="h-5 w-5 text-orange-400" />
                الموقع
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="bg-gray-800/30 rounded-lg p-3">
                <p className="text-sm text-gray-400 mb-1">العنوان</p>
                <p className="text-gray-200 font-medium">{property.address}</p>
              </div>
              <div className="bg-gray-800/30 rounded-lg p-3">
                <p className="text-sm text-gray-400 mb-1">المدينة والدولة</p>
                <p className="text-gray-200 font-medium">{property.city}, {property.country}</p>
              </div>
              {property.location && (
                <div className="bg-gray-800/30 rounded-lg p-3">
                  <p className="text-sm text-gray-400 mb-1">المنطقة</p>
                  <p className="text-gray-200 font-medium">{property.location}</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Agent Info */}
          {property.agent && (
            <Card className="property-card-dark">
              <CardHeader>
                <CardTitle className="arabic-heading">معلومات الوكيل</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <p className="font-medium text-white">{property.agent.name}</p>
                  <p className="text-gray-400 text-sm">{property.agent.email}</p>
                </div>
                <div className="flex gap-2">
                  <Button size="sm" className="btn-primary flex-1">
                    <Phone className="h-4 w-4 ml-2" />
                    اتصال
                  </Button>
                  <Button size="sm" variant="outline" className="flex-1">
                    <Mail className="h-4 w-4 ml-2" />
                    رسالة
                  </Button>
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      </div>

      {/* Image Modal */}
      {isImageModalOpen && (
        <div className="fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4">
          <div className="relative max-w-4xl max-h-full">
            <button
              onClick={() => setIsImageModalOpen(false)}
              className="absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 z-10"
            >
              <X className="h-6 w-6" />
            </button>

            <img
              src={property.images[currentImageIndex]}
              alt={property.title}
              className="max-w-full max-h-full object-contain"
            />

            {property.images.length > 1 && (
              <>
                <button
                  onClick={prevImage}
                  className="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
                >
                  <ChevronLeft className="h-6 w-6" />
                </button>
                <button
                  onClick={nextImage}
                  className="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3"
                >
                  <ChevronRight className="h-6 w-6" />
                </button>
              </>
            )}

            <div className="absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 rounded-full px-4 py-2">
              <span className="text-white text-sm">
                {currentImageIndex + 1} من {property.images.length}
              </span>
            </div>
          </div>
        </div>
      )}
    </div>
  )
}
