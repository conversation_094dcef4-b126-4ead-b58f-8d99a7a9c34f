"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("middleware",{

/***/ "(middleware)/./middleware.ts":
/*!***********************!*\
  !*** ./middleware.ts ***!
  \***********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   config: () => (/* binding */ config),\n/* harmony export */   middleware: () => (/* binding */ middleware)\n/* harmony export */ });\n/* harmony import */ var next_server__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/server */ \"(middleware)/./node_modules/next/dist/esm/api/server.js\");\n\n// Define protected routes patterns\nconst protectedRoutes = [\n    \"/dashboard(.*)\",\n    \"/api/dashboard(.*)\",\n    \"/api/properties(.*)\",\n    \"/api/campaigns(.*)\",\n    \"/api/users(.*)\"\n];\n// Define public routes that should redirect to dashboard if authenticated\nconst publicRoutes = [\n    \"/\",\n    \"/home(.*)\",\n    \"/about(.*)\",\n    \"/contact(.*)\"\n];\n// Admin-only routes are checked in the server component\nasync function middleware(req) {\n    // Get the session token from the cookies\n    // Check for both possible cookie names (secure and non-secure)\n    const sessionToken = req.cookies.get(\"next-auth.session-token\")?.value || req.cookies.get(\"__Secure-next-auth.session-token\")?.value;\n    const isAuthenticated = !!sessionToken;\n    // Clean up unnecessary cookies and reduce logging\n    const url = req.nextUrl.clone();\n    const path = url.pathname;\n    // Only log for debugging in development\n    if (true) {\n        console.log(`Middleware: Path=${path}, Authenticated=${isAuthenticated}`);\n    }\n    // Check if the path is a protected route\n    const isProtectedRoute = protectedRoutes.some((pattern)=>{\n        const regex = new RegExp(`^${pattern}$`);\n        return regex.test(path);\n    });\n    // We'll check admin-only routes in the server component\n    // If the user is not authenticated and trying to access a protected route\n    if (!isAuthenticated && isProtectedRoute) {\n        // Store the original URL to redirect back after authentication\n        const signInUrl = new URL('/sign-in', req.url);\n        signInUrl.searchParams.set('redirect_url', path);\n        // Redirect to the sign-in page\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(signInUrl);\n    }\n    // Check if the path is a public route\n    const isPublicRoute = path === \"/\" || publicRoutes.some((pattern)=>{\n        if (pattern === \"/\") return path === \"/\";\n        const regex = new RegExp(`^${pattern}$`);\n        return regex.test(path);\n    });\n    // If the user is authenticated and trying to access auth pages or public routes\n    if (isAuthenticated && (path === '/sign-in' || path === '/sign-up' || isPublicRoute)) {\n        console.log(`Redirecting authenticated user from ${path} to dashboard analytics`);\n        // Redirect to the dashboard analytics page\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/analytics', req.url));\n    }\n    // Redirect from /dashboard to /dashboard/analytics\n    if (path === '/dashboard' && isAuthenticated) {\n        console.log(`Redirecting from /dashboard to /dashboard/analytics`);\n        return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.redirect(new URL('/dashboard/analytics', req.url));\n    }\n    // For admin-only routes, we'll check the role in the server component\n    // since we can't securely check the role in the middleware\n    // The role is now an enum: USER, ADMIN, AGENT, CLIENT\n    return next_server__WEBPACK_IMPORTED_MODULE_0__.NextResponse.next();\n}\n// Configure the middleware to run on specific paths\nconst config = {\n    matcher: [\n        // Include only the paths we want to protect or handle\n        '/dashboard',\n        '/dashboard/:path*',\n        '/api/dashboard/:path*',\n        '/api/properties/:path*',\n        '/api/campaigns/:path*',\n        '/api/users/:path*',\n        '/sign-in',\n        '/sign-up',\n        '/sign-out',\n        '/'\n    ]\n};\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(middleware)/./middleware.ts\n");

/***/ })

});