import {
  generateUploadButton,
  generateUploadDropzone,
  generateReactHelpers,
} from "@uploadthing/react";

// Import the file router type from backend
export type OurFileRouter = {
  propertyImageUploader: any;
  avatarUploader: any;
  documentUploader: any;
};

// Configure UploadThing to use the Express backend
const { useUploadThing, uploadFiles } = generateReactHelpers<OurFileRouter>({
  url: `${process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:5000"}/api/v1/uploadthing`,
});

export const UploadButton = generateUploadButton<OurFileRouter>({
  url: `${process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:5000"}/api/v1/uploadthing`,
});

export const UploadDropzone = generateUploadDropzone<OurFileRouter>({
  url: `${process.env.NEXT_PUBLIC_BACKEND_URL || "http://localhost:5000"}/api/v1/uploadthing`,
});

export { useUploadThing, uploadFiles };
