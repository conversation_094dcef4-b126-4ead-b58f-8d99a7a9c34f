"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/page",{

/***/ "(app-pages-browser)/./components/properties/PropertyShowComponent.tsx":
/*!*********************************************************!*\
  !*** ./components/properties/PropertyShowComponent.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyShowComponent: () => (/* binding */ PropertyShowComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _lib_utils_formatPrice__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/lib/utils/formatPrice */ \"(app-pages-browser)/./lib/utils/formatPrice.ts\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bed.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bath.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _styles_arabic_properties_css__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/styles/arabic-properties.css */ \"(app-pages-browser)/./styles/arabic-properties.css\");\n/* __next_internal_client_entry_do_not_use__ PropertyShowComponent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\nfunction PropertyShowComponent(param) {\n    let { property } = param;\n    _s();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage)();\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isImageModalOpen, setIsImageModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'AVAILABLE':\n                return 'bg-green-600 text-white';\n            case 'SOLD':\n                return 'bg-red-600 text-white';\n            case 'RENTED':\n                return 'bg-blue-600 text-white';\n            case 'PENDING':\n                return 'bg-yellow-600 text-black';\n            default:\n                return 'bg-gray-600 text-white';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'AVAILABLE':\n                return 'متاح';\n            case 'SOLD':\n                return 'مباع';\n            case 'RENTED':\n                return 'مؤجر';\n            case 'PENDING':\n                return 'قيد المراجعة';\n            default:\n                return status;\n        }\n    };\n    const getTypeText = (type)=>{\n        switch(type){\n            case 'APARTMENT':\n                return 'شقة';\n            case 'VILLA':\n                return 'فيلا';\n            case 'TOWNHOUSE':\n                return 'تاون هاوس';\n            case 'PENTHOUSE':\n                return 'بنتهاوس';\n            case 'STUDIO':\n                return 'استوديو';\n            case 'OFFICE':\n                return 'مكتب';\n            case 'SHOP':\n                return 'محل تجاري';\n            case 'WAREHOUSE':\n                return 'مستودع';\n            case 'LAND':\n                return 'أرض';\n            case 'BUILDING':\n                return 'مبنى';\n            default:\n                return type;\n        }\n    };\n    const nextImage = ()=>{\n        setCurrentImageIndex((prev)=>prev === property.images.length - 1 ? 0 : prev + 1);\n    };\n    const prevImage = ()=>{\n        setCurrentImageIndex((prev)=>prev === 0 ? property.images.length - 1 : prev - 1);\n    };\n    const openImageModal = (index)=>{\n        setCurrentImageIndex(index);\n        setIsImageModalOpen(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            property.images && property.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"property-card-dark overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"p-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-video bg-gray-800 overflow-hidden relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: property.images[currentImageIndex],\n                                            alt: property.title,\n                                            className: \"w-full h-full object-cover cursor-pointer transition-transform duration-300 hover:scale-105\",\n                                            onClick: ()=>openImageModal(currentImageIndex)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 145,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 153,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            children: \"انقر للتكبير\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                    lineNumber: 144,\n                                    columnNumber: 15\n                                }, this),\n                                property.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: prevImage,\n                                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white rounded-full p-3 transition-all duration-200 opacity-0 group-hover:opacity-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 167,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 163,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: nextImage,\n                                            className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white rounded-full p-3 transition-all duration-200 opacity-0 group-hover:opacity-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 173,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 169,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm\",\n                                    children: [\n                                        currentImageIndex + 1,\n                                        \" / \",\n                                        property.images.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                    lineNumber: 179,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2\",\n                                    children: property.images.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentImageIndex(index),\n                                            className: \"w-3 h-3 rounded-full transition-all duration-200 \".concat(index === currentImageIndex ? 'bg-white scale-110' : 'bg-white/50 hover:bg-white/70')\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 186,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                    lineNumber: 184,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                            lineNumber: 143,\n                            columnNumber: 13\n                        }, this),\n                        property.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-gray-900/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-6 gap-3\",\n                                children: [\n                                    property.images.slice(0, 6).map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-square bg-gray-800 rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-200 hover:scale-105 \".concat(index === currentImageIndex ? 'border-blue-500 shadow-lg shadow-blue-500/25' : 'border-transparent hover:border-gray-600'),\n                                            onClick: ()=>setCurrentImageIndex(index),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: image,\n                                                alt: \"\".concat(property.title, \" \").concat(index + 1),\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 213,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 204,\n                                            columnNumber: 21\n                                        }, this)),\n                                    property.images.length > 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-square bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 text-sm border-2 border-dashed border-gray-600\",\n                                        children: [\n                                            \"+\",\n                                            property.images.length - 6\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 221,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 202,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                            lineNumber: 201,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                    lineNumber: 142,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                lineNumber: 141,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark border-l-4 border-l-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        className: \"pb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"arabic-heading text-xl flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                            className: \"h-6 w-6 text-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 241,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"معلومات العقار\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 240,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                            className: getStatusColor(property.status),\n                                                            children: getStatusText(property.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 245,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                            className: \"bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-medium\",\n                                                            children: \"⭐ مميز\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 249,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 244,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 239,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 238,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-6 w-6 text-blue-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 261,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: \"نوع العقار\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                    lineNumber: 264,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold text-white\",\n                                                                    children: getTypeText(property.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                    lineNumber: 265,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 263,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 258,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                children: [\n                                                    property.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/30 rounded-lg p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 274,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: property.bedrooms\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 275,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"غرف نوم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 273,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/30 rounded-lg p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 282,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: property.bathrooms\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 283,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"حمامات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 284,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 281,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/30 rounded-lg p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: property.area\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"متر مربع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 292,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 289,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.parking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/30 rounded-lg p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: property.parking\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"مواقف سيارات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 300,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 297,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 271,\n                                                columnNumber: 15\n                                            }, this),\n                                            property.yearBuilt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/30 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 309,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: \"سنة البناء: \"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 310,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-white\",\n                                                            children: property.yearBuilt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 311,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 308,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 307,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-3\",\n                                                children: [\n                                                    property.furnished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-green-400 border-green-400 bg-green-400/10 px-3 py-1\",\n                                                        children: \"✓ مفروش\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 319,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.petFriendly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-green-400 border-green-400 bg-green-400/10 px-3 py-1\",\n                                                        children: \"✓ يسمح بالحيوانات الأليفة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 324,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.status === 'SOLD' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        className: \"bg-red-600 text-white px-3 py-1\",\n                                                        children: \"\\uD83C\\uDFF7️ مباع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 329,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 317,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 256,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 237,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"arabic-heading\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 340,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 339,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 leading-relaxed whitespace-pre-wrap\",\n                                            children: property.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 343,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 342,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 11\n                            }, this),\n                            property.features && property.features.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"arabic-heading\",\n                                            children: \"المميزات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 353,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 352,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                            children: property.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-gray-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 359,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: feature\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 360,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 358,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 356,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 355,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 351,\n                                columnNumber: 13\n                            }, this),\n                            property.amenities && property.amenities.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"arabic-heading\",\n                                            children: \"الخدمات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 372,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                            children: property.amenities.map((amenity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-gray-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 378,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: amenity\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 379,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 377,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 375,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 370,\n                                columnNumber: 13\n                            }, this),\n                            property.utilities && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"arabic-heading\",\n                                            children: \"المرافق المشمولة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 391,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 390,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: property.utilities\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 394,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 393,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                        lineNumber: 235,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark border-t-4 border-t-green-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-1\",\n                                                        children: \"السعر\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 407,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl font-bold text-white mb-2\",\n                                                        children: (0,_lib_utils_formatPrice__WEBPACK_IMPORTED_MODULE_6__.formatPriceArabic)(property.price, property.currency)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 408,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.status === 'SOLD' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-flex items-center gap-2 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                                        children: \"\\uD83C\\uDFF7️ تم البيع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 412,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 406,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center gap-4 pt-4 border-t border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 420,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.viewCount,\n                                                                    \" مشاهدة\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 421,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 419,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 text-yellow-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 425,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"مميز\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 426,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 424,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 418,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                    lineNumber: 404,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 403,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark border-l-4 border-l-orange-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"arabic-heading flex items-center gap-2 text-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                    className: \"h-5 w-5 text-orange-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 438,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الموقع\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 437,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 436,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/30 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-1\",\n                                                        children: \"العنوان\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 444,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-200 font-medium\",\n                                                        children: property.address\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 445,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 443,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/30 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-1\",\n                                                        children: \"المدينة والدولة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 448,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-200 font-medium\",\n                                                        children: [\n                                                            property.city,\n                                                            \", \",\n                                                            property.country\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 449,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 447,\n                                                columnNumber: 15\n                                            }, this),\n                                            property.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/30 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-1\",\n                                                        children: \"المنطقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 453,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-200 font-medium\",\n                                                        children: property.location\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 454,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 452,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 442,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 435,\n                                columnNumber: 11\n                            }, this),\n                            property.agent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"arabic-heading\",\n                                            children: \"معلومات الوكيل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 464,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 463,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: property.agent.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: property.agent.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        size: \"sm\",\n                                                        className: \"btn-primary flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4 ml-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 473,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"اتصال\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 472,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                                                className: \"h-4 w-4 ml-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 477,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"رسالة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 476,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 471,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 466,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 462,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                        lineNumber: 401,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                lineNumber: 233,\n                columnNumber: 7\n            }, this),\n            isImageModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-4xl max-h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsImageModalOpen(false),\n                            className: \"absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_21__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 495,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                            lineNumber: 491,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: property.images[currentImageIndex],\n                            alt: property.title,\n                            className: \"max-w-full max-h-full object-contain\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                            lineNumber: 498,\n                            columnNumber: 13\n                        }, this),\n                        property.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: prevImage,\n                                    className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 510,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                    lineNumber: 506,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: nextImage,\n                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 516,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                    lineNumber: 512,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 rounded-full px-4 py-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white text-sm\",\n                                children: [\n                                    currentImageIndex + 1,\n                                    \" من \",\n                                    property.images.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 522,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                            lineNumber: 521,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                    lineNumber: 490,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                lineNumber: 489,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n        lineNumber: 138,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyShowComponent, \"BK1wSWwFHW+UKgmqye7GXKdYpRE=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage\n    ];\n});\n_c = PropertyShowComponent;\nvar _c;\n$RefreshReg$(_c, \"PropertyShowComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/properties/PropertyShowComponent.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/utils/formatPrice.ts":
/*!**********************************!*\
  !*** ./lib/utils/formatPrice.ts ***!
  \**********************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   SUPPORTED_CURRENCIES: () => (/* binding */ SUPPORTED_CURRENCIES),\n/* harmony export */   formatPrice: () => (/* binding */ formatPrice),\n/* harmony export */   formatPriceArabic: () => (/* binding */ formatPriceArabic),\n/* harmony export */   formatPriceEnglish: () => (/* binding */ formatPriceEnglish),\n/* harmony export */   getCurrencySymbol: () => (/* binding */ getCurrencySymbol),\n/* harmony export */   isSupportedCurrency: () => (/* binding */ isSupportedCurrency)\n/* harmony export */ });\n/**\n * Utility function to format price with proper currency handling\n * Handles invalid currencies and provides fallback formatting\n */ function formatPrice(price, currency) {\n    let locale = arguments.length > 2 && arguments[2] !== void 0 ? arguments[2] : 'ar-AE';\n    // Ensure currency is valid, default to USD if not provided\n    const validCurrency = currency && currency.length === 3 ? currency.toUpperCase() : 'USD';\n    // Validate price\n    if (typeof price !== 'number' || isNaN(price)) {\n        return '0 ' + validCurrency;\n    }\n    try {\n        return new Intl.NumberFormat(locale, {\n            style: 'currency',\n            currency: validCurrency,\n            minimumFractionDigits: 0,\n            maximumFractionDigits: 0\n        }).format(price);\n    } catch (error) {\n        // Fallback to simple number formatting if currency is invalid\n        try {\n            return new Intl.NumberFormat(locale, {\n                minimumFractionDigits: 0,\n                maximumFractionDigits: 0\n            }).format(price) + ' ' + validCurrency;\n        } catch (fallbackError) {\n            // Ultimate fallback\n            return price.toLocaleString() + ' ' + validCurrency;\n        }\n    }\n}\n/**\n * Format price specifically for Arabic locale\n */ function formatPriceArabic(price, currency) {\n    return formatPrice(price, currency, 'ar-AE');\n}\n/**\n * Format price specifically for English locale\n */ function formatPriceEnglish(price, currency) {\n    return formatPrice(price, currency, 'en-US');\n}\n/**\n * Get supported currencies list\n */ const SUPPORTED_CURRENCIES = [\n    'USD',\n    'EUR',\n    'GBP',\n    'AED',\n    'SAR',\n    'QAR',\n    'KWD',\n    'BHD',\n    'OMR'\n];\n/**\n * Validate if currency code is supported\n */ function isSupportedCurrency(currency) {\n    return SUPPORTED_CURRENCIES.includes(currency);\n}\n/**\n * Get currency symbol for a given currency code\n */ function getCurrencySymbol(currency) {\n    let locale = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : 'ar-AE';\n    const validCurrency = currency && currency.length === 3 ? currency.toUpperCase() : 'USD';\n    try {\n        const formatter = new Intl.NumberFormat(locale, {\n            style: 'currency',\n            currency: validCurrency,\n            minimumFractionDigits: 0\n        });\n        // Extract just the currency symbol\n        const parts = formatter.formatToParts(0);\n        const currencyPart = parts.find((part)=>part.type === 'currency');\n        return (currencyPart === null || currencyPart === void 0 ? void 0 : currencyPart.value) || validCurrency;\n    } catch (error) {\n        return validCurrency;\n    }\n}\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/utils/formatPrice.ts\n"));

/***/ })

});