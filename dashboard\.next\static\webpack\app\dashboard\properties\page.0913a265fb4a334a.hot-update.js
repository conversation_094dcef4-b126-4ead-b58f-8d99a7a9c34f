"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/page.tsx":
/*!*******************************************!*\
  !*** ./app/dashboard/properties/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PropertiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\nfunction PropertiesPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_8__.useSimpleLanguage)();\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('الكل');\n    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('الكل');\n    // Removed dialog states - now using dedicated pages\n    // Fetch properties and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertiesPage.useEffect\": ()=>{\n            fetchProperties();\n            fetchStats();\n        }\n    }[\"PropertiesPage.useEffect\"], [\n        searchTerm,\n        filterType,\n        filterStatus\n    ]);\n    const fetchProperties = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams();\n            if (searchTerm) params.append('search', searchTerm);\n            if (filterType && filterType !== 'الكل' && filterType !== 'ALL') params.append('type', filterType);\n            if (filterStatus && filterStatus !== 'الكل' && filterStatus !== 'ALL') params.append('status', filterStatus);\n            params.append('isActive', 'true');\n            const response = await fetch(\"/api/v1/properties?\".concat(params));\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setProperties(data.data.properties);\n                }\n            } else {\n                // Fallback to mock data if backend is not available\n                console.log('Backend not available, using mock data');\n                setProperties([\n                    {\n                        id: '1',\n                        title: 'Luxury Villa in Dubai Marina',\n                        titleAr: 'فيلا فاخرة في دبي مارينا',\n                        description: 'Beautiful 4-bedroom villa with sea view',\n                        descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة على البحر',\n                        price: 2500000,\n                        currency: 'AED',\n                        type: 'VILLA',\n                        status: 'AVAILABLE',\n                        bedrooms: 4,\n                        bathrooms: 3,\n                        area: 350,\n                        location: 'Dubai Marina',\n                        locationAr: 'دبي مارينا',\n                        address: '123 Marina Walk',\n                        addressAr: '123 ممشى المارينا',\n                        city: 'Dubai',\n                        cityAr: 'دبي',\n                        images: [\n                            '/placeholder.jpg'\n                        ],\n                        features: [\n                            'Swimming Pool',\n                            'Gym',\n                            'Parking'\n                        ],\n                        featuresAr: [\n                            'مسبح',\n                            'صالة رياضية',\n                            'موقف سيارات'\n                        ],\n                        amenities: [\n                            '24/7 Security',\n                            'Concierge'\n                        ],\n                        amenitiesAr: [\n                            'أمن 24/7',\n                            'خدمة الكونسيرج'\n                        ],\n                        isFeatured: true,\n                        isActive: true,\n                        viewCount: 125,\n                        createdAt: new Date().toISOString()\n                    },\n                    {\n                        id: '2',\n                        title: 'Modern Apartment in Downtown',\n                        titleAr: 'شقة حديثة في وسط المدينة',\n                        description: 'Spacious 2-bedroom apartment',\n                        descriptionAr: 'شقة واسعة من غرفتي نوم',\n                        price: 1200000,\n                        currency: 'AED',\n                        type: 'APARTMENT',\n                        status: 'AVAILABLE',\n                        bedrooms: 2,\n                        bathrooms: 2,\n                        area: 120,\n                        location: 'Downtown Dubai',\n                        locationAr: 'وسط مدينة دبي',\n                        address: '456 Sheikh Zayed Road',\n                        addressAr: '456 شارع الشيخ زايد',\n                        city: 'Dubai',\n                        cityAr: 'دبي',\n                        images: [\n                            '/placeholder.jpg'\n                        ],\n                        features: [\n                            'Balcony',\n                            'Built-in Wardrobes'\n                        ],\n                        featuresAr: [\n                            'شرفة',\n                            'خزائن مدمجة'\n                        ],\n                        amenities: [\n                            'Gym',\n                            'Pool'\n                        ],\n                        amenitiesAr: [\n                            'صالة رياضية',\n                            'مسبح'\n                        ],\n                        isFeatured: false,\n                        isActive: true,\n                        viewCount: 89,\n                        createdAt: new Date().toISOString()\n                    }\n                ]);\n            }\n        } catch (error) {\n            console.error('Error fetching properties:', error);\n            // Fallback to empty array on error\n            setProperties([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch('/api/v1/properties/stats');\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setStats(data.data);\n                }\n            } else {\n                // Fallback to mock stats if backend is not available\n                console.log('Backend not available, using mock stats');\n                setStats({\n                    total: 2,\n                    available: 2,\n                    sold: 0,\n                    rented: 0,\n                    featured: 1,\n                    byType: {\n                        APARTMENT: 1,\n                        VILLA: 1,\n                        TOWNHOUSE: 0,\n                        PENTHOUSE: 0,\n                        STUDIO: 0,\n                        OFFICE: 0,\n                        SHOP: 0,\n                        WAREHOUSE: 0,\n                        LAND: 0,\n                        BUILDING: 0\n                    }\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n            // Fallback to default stats on error\n            setStats({\n                total: 0,\n                available: 0,\n                sold: 0,\n                rented: 0,\n                featured: 0,\n                byType: {\n                    APARTMENT: 0,\n                    VILLA: 0,\n                    TOWNHOUSE: 0,\n                    PENTHOUSE: 0,\n                    STUDIO: 0,\n                    OFFICE: 0,\n                    SHOP: 0,\n                    WAREHOUSE: 0,\n                    LAND: 0,\n                    BUILDING: 0\n                }\n            });\n        }\n    };\n    const handleDeleteProperty = async (id)=>{\n        if (!confirm(language === 'ar' ? 'هل أنت متأكد من حذف هذا العقار؟' : 'Are you sure you want to delete this property?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/v1/properties/\".concat(id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                fetchProperties();\n                fetchStats();\n            }\n        } catch (error) {\n            console.error('Error deleting property:', error);\n        }\n    };\n    const formatPrice = (price, currency)=>{\n        return new Intl.NumberFormat(language === 'ar' ? 'ar-AE' : 'en-US', {\n            style: 'currency',\n            currency: currency,\n            minimumFractionDigits: 0\n        }).format(price);\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'AVAILABLE':\n                return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300';\n            case 'SOLD':\n                return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-300';\n            case 'RENTED':\n                return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300';\n            case 'RESERVED':\n                return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-300';\n            default:\n                return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300';\n        }\n    };\n    const getPropertyTitle = (property)=>{\n        return language === 'ar' && property.titleAr ? property.titleAr : property.title;\n    };\n    const getPropertyLocation = (property)=>{\n        return language === 'ar' && property.locationAr ? property.locationAr : property.location;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                children: language === 'ar' ? 'العقارات' : 'Properties'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 273,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                children: language === 'ar' ? 'إدارة العقارات والممتلكات' : 'Manage properties and real estate'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 276,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 272,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>router.push('/dashboard/properties/create'),\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 284,\n                                columnNumber: 11\n                            }, this),\n                            language === 'ar' ? 'إضافة عقار' : 'Add Property'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 280,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 271,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'إجمالي العقارات' : 'Total Properties'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 294,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: stats.total\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 299,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 298,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 292,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'متاح' : 'Available'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 304,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 303,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: stats.available\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 309,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 308,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 302,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'مباع' : 'Sold'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 314,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 313,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-red-600\",\n                                    children: stats.sold\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 319,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 318,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 312,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'مؤجر' : 'Rented'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 324,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 323,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: stats.rented\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 329,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 328,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 322,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'مميز' : 'Featured'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 334,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 333,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: stats.featured\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 339,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 338,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 332,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 291,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 348,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: language === 'ar' ? 'البحث في العقارات...' : 'Search properties...',\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 349,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 347,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                        value: filterType,\n                        onValueChange: setFilterType,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                className: \"w-full sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                    placeholder: language === 'ar' ? 'نوع العقار' : 'Property Type'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 358,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"الكل\",\n                                        children: \"جميع الأنواع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 361,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"APARTMENT\",\n                                        children: \"شقة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 362,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"VILLA\",\n                                        children: \"فيلا\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 363,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"TOWNHOUSE\",\n                                        children: \"تاون هاوس\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 364,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"OFFICE\",\n                                        children: \"مكتب\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 365,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 360,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                        value: filterStatus,\n                        onValueChange: setFilterStatus,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                className: \"w-full sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                    placeholder: language === 'ar' ? 'الحالة' : 'Status'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 370,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"الكل\",\n                                        children: \"جميع الحالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"AVAILABLE\",\n                                        children: \"متاح\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"SOLD\",\n                                        children: \"مباع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 375,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"RENTED\",\n                                        children: \"مؤجر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 376,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 372,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 368,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: viewMode === 'grid' ? 'default' : 'outline',\n                                size: \"sm\",\n                                onClick: ()=>setViewMode('grid'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 385,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 380,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: viewMode === 'list' ? 'default' : 'outline',\n                                size: \"sm\",\n                                onClick: ()=>setViewMode('list'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 392,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 387,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 379,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 346,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                    lineNumber: 400,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 399,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4',\n                children: properties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"overflow-hidden hover:shadow-lg transition-shadow\",\n                        children: viewMode === 'grid' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                property.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-48 bg-gray-200 dark:bg-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: property.images[0],\n                                            alt: getPropertyTitle(property),\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 410,\n                                            columnNumber: 23\n                                        }, this),\n                                        property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: \"absolute top-2 left-2 bg-purple-600\",\n                                            children: language === 'ar' ? 'مميز' : 'Featured'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 416,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 409,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-lg truncate\",\n                                                    children: getPropertyTitle(property)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 424,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    className: getStatusColor(property.status),\n                                                    children: property.status\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 425,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 423,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 text-sm mb-2\",\n                                            children: getPropertyLocation(property)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 429,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl font-bold text-blue-600\",\n                                                    children: formatPrice(property.price, property.currency)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 text-sm text-gray-500\",\n                                                    children: [\n                                                        property.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                property.bedrooms,\n                                                                \" \",\n                                                                language === 'ar' ? 'غرف' : 'bed'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 437,\n                                                            columnNumber: 47\n                                                        }, this),\n                                                        property.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                property.bathrooms,\n                                                                \" \",\n                                                                language === 'ar' ? 'حمام' : 'bath'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 438,\n                                                            columnNumber: 48\n                                                        }, this),\n                                                        property.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                property.area,\n                                                                \"m\\xb2\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 439,\n                                                            columnNumber: 43\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 436,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>router.push(\"/dashboard/properties/\".concat(property.id)),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 445,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 444,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>router.push(\"/dashboard/properties/\".concat(property.id, \"/edit\")),\n                                                            className: \"hover:bg-blue-50 hover:border-blue-300 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 453,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleDeleteProperty(property.id),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 456,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 455,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 443,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        language === 'ar' ? 'المشاهدات:' : 'Views:',\n                                                        \" \",\n                                                        property.viewCount\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 459,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 442,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 422,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    property.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: property.images[0],\n                                            alt: getPropertyTitle(property),\n                                            className: \"w-full h-full object-cover rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 470,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 469,\n                                        columnNumber: 23\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-lg\",\n                                                        children: getPropertyTitle(property)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 479,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: getStatusColor(property.status),\n                                                                children: property.status\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 481,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: \"bg-purple-600\",\n                                                                children: language === 'ar' ? 'مميز' : 'Featured'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 485,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 480,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 478,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 text-sm mb-2\",\n                                                children: getPropertyLocation(property)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 491,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold text-blue-600\",\n                                                        children: formatPrice(property.price, property.currency)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 495,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-4 text-sm text-gray-500\",\n                                                        children: [\n                                                            property.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.bedrooms,\n                                                                    \" \",\n                                                                    language === 'ar' ? 'غرف' : 'bed'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 499,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            property.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.bathrooms,\n                                                                    \" \",\n                                                                    language === 'ar' ? 'حمام' : 'bath'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 500,\n                                                                columnNumber: 50\n                                                            }, this),\n                                                            property.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.area,\n                                                                    \"m\\xb2\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 501,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    language === 'ar' ? 'المشاهدات:' : 'Views:',\n                                                                    \" \",\n                                                                    property.viewCount\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 502,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 498,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>setSelectedProperty(property),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                    lineNumber: 506,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 505,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>router.push(\"/dashboard/properties/edit/\".concat(property.id)),\n                                                                className: \"hover:bg-blue-50 hover:border-blue-300 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                    lineNumber: 514,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteProperty(property.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                    lineNumber: 517,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 516,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 494,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 477,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 467,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                            lineNumber: 466,\n                            columnNumber: 17\n                        }, this)\n                    }, property.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 405,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 403,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n        lineNumber: 269,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertiesPage, \"fSK+ki2zuOH8XoJ/w9r3SeZEO7o=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_8__.useSimpleLanguage\n    ];\n});\n_c = PropertiesPage;\nvar _c;\n$RefreshReg$(_c, \"PropertiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/page.tsx\n"));

/***/ })

});