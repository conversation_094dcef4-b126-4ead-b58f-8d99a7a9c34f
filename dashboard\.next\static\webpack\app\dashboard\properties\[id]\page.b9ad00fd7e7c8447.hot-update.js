"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/[id]/page",{

/***/ "(app-pages-browser)/./components/properties/PropertyShowComponent.tsx":
/*!*********************************************************!*\
  !*** ./components/properties/PropertyShowComponent.tsx ***!
  \*********************************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   PropertyShowComponent: () => (/* binding */ PropertyShowComponent)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-left.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/chevron-right.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/house.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bed.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/bath.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/car.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/calendar.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/heart.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/map-pin.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/phone.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/mail.js\");\n/* harmony import */ var _barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__ = __webpack_require__(/*! __barrel_optimize__?names=Bath,Bed,Calendar,Car,ChevronLeft,ChevronRight,Eye,Heart,Home,Mail,MapPin,Phone,Square,X!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/x.js\");\n/* harmony import */ var _styles_arabic_properties_css__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/styles/arabic-properties.css */ \"(app-pages-browser)/./styles/arabic-properties.css\");\n/* __next_internal_client_entry_do_not_use__ PropertyShowComponent auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\nfunction PropertyShowComponent(param) {\n    let { property } = param;\n    _s();\n    const { t } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage)();\n    const [currentImageIndex, setCurrentImageIndex] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(0);\n    const [isImageModalOpen, setIsImageModalOpen] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(false);\n    const formatPrice = (price, currency)=>{\n        // Ensure currency is valid, default to USD if not provided\n        const validCurrency = currency && currency.length === 3 ? currency : 'USD';\n        try {\n            return new Intl.NumberFormat('ar-AE', {\n                style: 'currency',\n                currency: validCurrency,\n                minimumFractionDigits: 0\n            }).format(price);\n        } catch (error) {\n            // Fallback to simple number formatting if currency is invalid\n            return new Intl.NumberFormat('ar-AE', {\n                minimumFractionDigits: 0\n            }).format(price) + ' ' + validCurrency;\n        }\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'AVAILABLE':\n                return 'bg-green-600 text-white';\n            case 'SOLD':\n                return 'bg-red-600 text-white';\n            case 'RENTED':\n                return 'bg-blue-600 text-white';\n            case 'PENDING':\n                return 'bg-yellow-600 text-black';\n            default:\n                return 'bg-gray-600 text-white';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'AVAILABLE':\n                return 'متاح';\n            case 'SOLD':\n                return 'مباع';\n            case 'RENTED':\n                return 'مؤجر';\n            case 'PENDING':\n                return 'قيد المراجعة';\n            default:\n                return status;\n        }\n    };\n    const getTypeText = (type)=>{\n        switch(type){\n            case 'APARTMENT':\n                return 'شقة';\n            case 'VILLA':\n                return 'فيلا';\n            case 'TOWNHOUSE':\n                return 'تاون هاوس';\n            case 'PENTHOUSE':\n                return 'بنتهاوس';\n            case 'STUDIO':\n                return 'استوديو';\n            case 'OFFICE':\n                return 'مكتب';\n            case 'SHOP':\n                return 'محل تجاري';\n            case 'WAREHOUSE':\n                return 'مستودع';\n            case 'LAND':\n                return 'أرض';\n            case 'BUILDING':\n                return 'مبنى';\n            default:\n                return type;\n        }\n    };\n    const nextImage = ()=>{\n        setCurrentImageIndex((prev)=>prev === property.images.length - 1 ? 0 : prev + 1);\n    };\n    const prevImage = ()=>{\n        setCurrentImageIndex((prev)=>prev === 0 ? property.images.length - 1 : prev - 1);\n    };\n    const openImageModal = (index)=>{\n        setCurrentImageIndex(index);\n        setIsImageModalOpen(true);\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            property.images && property.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                className: \"property-card-dark overflow-hidden\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                    className: \"p-0\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"relative group\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"aspect-video bg-gray-800 overflow-hidden relative\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: property.images[currentImageIndex],\n                                            alt: property.title,\n                                            className: \"w-full h-full object-cover cursor-pointer transition-transform duration-300 hover:scale-105\",\n                                            onClick: ()=>openImageModal(currentImageIndex)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 160,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute inset-0 bg-gradient-to-t from-black/50 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 168,\n                                            columnNumber: 17\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"absolute top-4 left-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm opacity-0 group-hover:opacity-100 transition-opacity duration-300\",\n                                            children: \"انقر للتكبير\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 171,\n                                            columnNumber: 17\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                    lineNumber: 159,\n                                    columnNumber: 15\n                                }, this),\n                                property.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: prevImage,\n                                            className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white rounded-full p-3 transition-all duration-200 opacity-0 group-hover:opacity-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 182,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 178,\n                                            columnNumber: 19\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: nextImage,\n                                            className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/60 hover:bg-black/80 text-white rounded-full p-3 transition-all duration-200 opacity-0 group-hover:opacity-100\",\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                className: \"h-5 w-5\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 188,\n                                                columnNumber: 21\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 184,\n                                            columnNumber: 19\n                                        }, this)\n                                    ]\n                                }, void 0, true),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-4 right-4 bg-black/70 text-white px-3 py-1 rounded-full text-sm\",\n                                    children: [\n                                        currentImageIndex + 1,\n                                        \" / \",\n                                        property.images.length\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                    lineNumber: 194,\n                                    columnNumber: 15\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"absolute bottom-4 left-1/2 transform -translate-x-1/2 flex gap-2\",\n                                    children: property.images.map((_, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                            onClick: ()=>setCurrentImageIndex(index),\n                                            className: \"w-3 h-3 rounded-full transition-all duration-200 \".concat(index === currentImageIndex ? 'bg-white scale-110' : 'bg-white/50 hover:bg-white/70')\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 201,\n                                            columnNumber: 19\n                                        }, this))\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                    lineNumber: 199,\n                                    columnNumber: 15\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                            lineNumber: 158,\n                            columnNumber: 13\n                        }, this),\n                        property.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"p-4 bg-gray-900/50\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"grid grid-cols-6 gap-3\",\n                                children: [\n                                    property.images.slice(0, 6).map((image, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"aspect-square bg-gray-800 rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-200 hover:scale-105 \".concat(index === currentImageIndex ? 'border-blue-500 shadow-lg shadow-blue-500/25' : 'border-transparent hover:border-gray-600'),\n                                            onClick: ()=>setCurrentImageIndex(index),\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                                src: image,\n                                                alt: \"\".concat(property.title, \" \").concat(index + 1),\n                                                className: \"w-full h-full object-cover\"\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 228,\n                                                columnNumber: 23\n                                            }, this)\n                                        }, index, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 219,\n                                            columnNumber: 21\n                                        }, this)),\n                                    property.images.length > 6 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"aspect-square bg-gray-800 rounded-lg flex items-center justify-center text-gray-400 text-sm border-2 border-dashed border-gray-600\",\n                                        children: [\n                                            \"+\",\n                                            property.images.length - 6\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 236,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 217,\n                                columnNumber: 17\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                            lineNumber: 216,\n                            columnNumber: 15\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                    lineNumber: 157,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                lineNumber: 156,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 lg:grid-cols-3 gap-6\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"lg:col-span-2 space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark border-l-4 border-l-blue-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        className: \"pb-4\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex items-center justify-between\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                                    className: \"arabic-heading text-xl flex items-center gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                            className: \"h-6 w-6 text-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 256,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        \"معلومات العقار\"\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 255,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                            className: getStatusColor(property.status),\n                                                            children: getStatusText(property.status)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 260,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                            className: \"bg-gradient-to-r from-yellow-500 to-yellow-600 text-black font-medium\",\n                                                            children: \"⭐ مميز\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 264,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 259,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 254,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 253,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-6\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/50 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-12 h-12 bg-blue-500/20 rounded-lg flex items-center justify-center\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                                className: \"h-6 w-6 text-blue-400\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 276,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 275,\n                                                            columnNumber: 19\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            children: [\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-sm text-gray-400\",\n                                                                    children: \"نوع العقار\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                    lineNumber: 279,\n                                                                    columnNumber: 21\n                                                                }, this),\n                                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                    className: \"text-lg font-semibold text-white\",\n                                                                    children: getTypeText(property.type)\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                    lineNumber: 280,\n                                                                    columnNumber: 21\n                                                                }, this)\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 278,\n                                                            columnNumber: 19\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 274,\n                                                    columnNumber: 17\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 273,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"grid grid-cols-2 md:grid-cols-4 gap-4\",\n                                                children: [\n                                                    property.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/30 rounded-lg p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 289,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: property.bedrooms\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 290,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"غرف نوم\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 291,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 288,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/30 rounded-lg p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 297,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: property.bathrooms\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 298,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"حمامات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 299,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 296,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/30 rounded-lg p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 305,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: property.area\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 306,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"متر مربع\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 307,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 304,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.parking && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"bg-gray-800/30 rounded-lg p-4 text-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                                                className: \"h-8 w-8 text-blue-400 mx-auto mb-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 313,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-2xl font-bold text-white\",\n                                                                children: property.parking\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 314,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                                className: \"text-sm text-gray-400\",\n                                                                children: \"مواقف سيارات\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 315,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 312,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 286,\n                                                columnNumber: 15\n                                            }, this),\n                                            property.yearBuilt && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/30 rounded-lg p-4\",\n                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-3\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                            className: \"h-5 w-5 text-blue-400\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 324,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"text-gray-300\",\n                                                            children: \"سنة البناء: \"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 325,\n                                                            columnNumber: 21\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            className: \"font-semibold text-white\",\n                                                            children: property.yearBuilt\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 326,\n                                                            columnNumber: 21\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 323,\n                                                    columnNumber: 19\n                                                }, this)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 322,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex flex-wrap gap-3\",\n                                                children: [\n                                                    property.furnished && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-green-400 border-green-400 bg-green-400/10 px-3 py-1\",\n                                                        children: \"✓ مفروش\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 334,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.petFriendly && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        variant: \"outline\",\n                                                        className: \"text-green-400 border-green-400 bg-green-400/10 px-3 py-1\",\n                                                        children: \"✓ يسمح بالحيوانات الأليفة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 339,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.status === 'SOLD' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_3__.Badge, {\n                                                        className: \"bg-red-600 text-white px-3 py-1\",\n                                                        children: \"\\uD83C\\uDFF7️ مباع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 344,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 332,\n                                                columnNumber: 15\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 271,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 252,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"arabic-heading\",\n                                            children: \"الوصف\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 355,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 354,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300 leading-relaxed whitespace-pre-wrap\",\n                                            children: property.description\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 358,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 357,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 353,\n                                columnNumber: 11\n                            }, this),\n                            property.features && property.features.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"arabic-heading\",\n                                            children: \"المميزات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 368,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 367,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                            children: property.features.map((feature, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-gray-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-blue-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 374,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: feature\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 375,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 373,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 371,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 13\n                            }, this),\n                            property.amenities && property.amenities.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"arabic-heading\",\n                                            children: \"الخدمات\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 387,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 386,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"grid grid-cols-1 md:grid-cols-2 gap-2\",\n                                            children: property.amenities.map((amenity, index)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex items-center gap-2 text-gray-300\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                            className: \"w-2 h-2 bg-green-400 rounded-full\"\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 393,\n                                                            columnNumber: 23\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: amenity\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                            lineNumber: 394,\n                                                            columnNumber: 23\n                                                        }, this)\n                                                    ]\n                                                }, index, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 392,\n                                                    columnNumber: 21\n                                                }, this))\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 390,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 389,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 385,\n                                columnNumber: 13\n                            }, this),\n                            property.utilities && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"arabic-heading\",\n                                            children: \"المرافق المشمولة\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 406,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 405,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-300\",\n                                            children: property.utilities\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 409,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 408,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 404,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                        lineNumber: 250,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"space-y-6\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark border-t-4 border-t-green-500\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                    className: \"p-6\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"text-center space-y-4\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-1\",\n                                                        children: \"السعر\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 422,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"text-4xl font-bold text-white mb-2\",\n                                                        children: formatPrice(property.price, property.currency)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 423,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.status === 'SOLD' && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"inline-flex items-center gap-2 bg-red-600 text-white px-3 py-1 rounded-full text-sm font-medium\",\n                                                        children: \"\\uD83C\\uDFF7️ تم البيع\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 427,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 421,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex items-center justify-center gap-4 pt-4 border-t border-gray-700\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 text-gray-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 435,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.viewCount,\n                                                                    \" مشاهدة\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 436,\n                                                                columnNumber: 21\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 434,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex items-center gap-2 text-yellow-400\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 440,\n                                                                columnNumber: 23\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: \"مميز\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 441,\n                                                                columnNumber: 23\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 439,\n                                                        columnNumber: 21\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 433,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 420,\n                                        columnNumber: 15\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                    lineNumber: 419,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 418,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark border-l-4 border-l-orange-500\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        className: \"pb-3\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"arabic-heading flex items-center gap-2 text-lg\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_17__[\"default\"], {\n                                                    className: \"h-5 w-5 text-orange-400\"\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                    lineNumber: 453,\n                                                    columnNumber: 17\n                                                }, this),\n                                                \"الموقع\"\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 452,\n                                            columnNumber: 15\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 451,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/30 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-1\",\n                                                        children: \"العنوان\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 459,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-200 font-medium\",\n                                                        children: property.address\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 460,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 458,\n                                                columnNumber: 15\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/30 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-1\",\n                                                        children: \"المدينة والدولة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 463,\n                                                        columnNumber: 17\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-200 font-medium\",\n                                                        children: [\n                                                            property.city,\n                                                            \", \",\n                                                            property.country\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 464,\n                                                        columnNumber: 17\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 462,\n                                                columnNumber: 15\n                                            }, this),\n                                            property.location && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"bg-gray-800/30 rounded-lg p-3\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-sm text-gray-400 mb-1\",\n                                                        children: \"المنطقة\"\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 468,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-200 font-medium\",\n                                                        children: property.location\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 469,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 467,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 457,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 450,\n                                columnNumber: 11\n                            }, this),\n                            property.agent && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.Card, {\n                                className: \"property-card-dark\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardHeader, {\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardTitle, {\n                                            className: \"arabic-heading\",\n                                            children: \"معلومات الوكيل\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_4__.CardContent, {\n                                        className: \"space-y-3\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"font-medium text-white\",\n                                                        children: property.agent.name\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 483,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                        className: \"text-gray-400 text-sm\",\n                                                        children: property.agent.email\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 484,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 482,\n                                                columnNumber: 17\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex gap-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        size: \"sm\",\n                                                        className: \"btn-primary flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_18__[\"default\"], {\n                                                                className: \"h-4 w-4 ml-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 488,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"اتصال\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 487,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_5__.Button, {\n                                                        size: \"sm\",\n                                                        variant: \"outline\",\n                                                        className: \"flex-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_19__[\"default\"], {\n                                                                className: \"h-4 w-4 ml-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                                lineNumber: 492,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"رسالة\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                        lineNumber: 491,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                                lineNumber: 486,\n                                                columnNumber: 17\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 481,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 477,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                        lineNumber: 416,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                lineNumber: 248,\n                columnNumber: 7\n            }, this),\n            isImageModalOpen && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"fixed inset-0 bg-black/90 z-50 flex items-center justify-center p-4\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"relative max-w-4xl max-h-full\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                            onClick: ()=>setIsImageModalOpen(false),\n                            className: \"absolute top-4 right-4 bg-black/50 hover:bg-black/70 text-white rounded-full p-2 z-10\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_20__[\"default\"], {\n                                className: \"h-6 w-6\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 510,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                            lineNumber: 506,\n                            columnNumber: 13\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                            src: property.images[currentImageIndex],\n                            alt: property.title,\n                            className: \"max-w-full max-h-full object-contain\"\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                            lineNumber: 513,\n                            columnNumber: 13\n                        }, this),\n                        property.images.length > 1 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: prevImage,\n                                    className: \"absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 525,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                    lineNumber: 521,\n                                    columnNumber: 17\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"button\", {\n                                    onClick: nextImage,\n                                    className: \"absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white rounded-full p-3\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Bath_Bed_Calendar_Car_ChevronLeft_ChevronRight_Eye_Heart_Home_Mail_MapPin_Phone_Square_X_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                        className: \"h-6 w-6\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                        lineNumber: 531,\n                                        columnNumber: 19\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                    lineNumber: 527,\n                                    columnNumber: 17\n                                }, this)\n                            ]\n                        }, void 0, true),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"absolute bottom-4 left-1/2 transform -translate-x-1/2 bg-black/50 rounded-full px-4 py-2\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                className: \"text-white text-sm\",\n                                children: [\n                                    currentImageIndex + 1,\n                                    \" من \",\n                                    property.images.length\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                                lineNumber: 537,\n                                columnNumber: 15\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                            lineNumber: 536,\n                            columnNumber: 13\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                    lineNumber: 505,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n                lineNumber: 504,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\components\\\\properties\\\\PropertyShowComponent.tsx\",\n        lineNumber: 153,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertyShowComponent, \"BK1wSWwFHW+UKgmqye7GXKdYpRE=\", false, function() {\n    return [\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_2__.useSimpleLanguage\n    ];\n});\n_c = PropertyShowComponent;\nvar _c;\n$RefreshReg$(_c, \"PropertyShowComponent\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/properties/PropertyShowComponent.tsx\n"));

/***/ })

});