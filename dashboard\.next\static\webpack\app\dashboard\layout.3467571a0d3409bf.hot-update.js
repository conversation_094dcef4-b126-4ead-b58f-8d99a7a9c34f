"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./lib/i18n/settings.ts":
/*!******************************!*\
  !*** ./lib/i18n/settings.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* binding */ defaultLanguage),\n/* harmony export */   languages: () => (/* binding */ languages),\n/* harmony export */   resources: () => (/* binding */ resources)\n/* harmony export */ });\nconst languages = [\n    \"ar\"\n];\nconst defaultLanguage = \"ar\" // Arabic only\n;\nconst resources = {\n    ar: {\n        translation: {\n            // Sidebar\n            \"sidebar.analytics\": \"التحليلات\",\n            \"sidebar.user\": \"لوحة التحكم\",\n            \"sidebar.clients\": \"العملاء\",\n            \"sidebar.messaging\": \"المراسلة\",\n            \"sidebar.marketing\": \"التسويق\",\n            \"sidebar.campaigns\": \"الحملات\",\n            \"sidebar.templates\": \"القوالب\",\n            \"sidebar.appointments\": \"المواعيد\",\n            \"sidebar.ai-chatbot\": \"الذكاء الاصطناعي\",\n            \"sidebar.users\": \"المستخدمين\",\n            \"sidebar.properties\": \"العقارات\",\n            \"sidebar.settings\": \"الإعدادات\",\n            \"sidebar.profile\": \"الملف الشخصي\",\n            // Properties\n            \"properties.title\": \"العقارات\",\n            \"properties.subtitle\": \"إدارة وإضافة العقارات الجديدة\",\n            \"properties.add\": \"إضافة عقار\",\n            \"properties.create\": \"إنشاء عقار جديد\",\n            \"properties.edit\": \"تعديل العقار\",\n            \"properties.delete\": \"حذف العقار\",\n            \"properties.view\": \"عرض العقار\",\n            \"properties.save\": \"حفظ العقار\",\n            \"properties.cancel\": \"إلغاء\",\n            \"properties.loading\": \"جاري التحميل...\",\n            \"properties.success\": \"تم حفظ العقار بنجاح\",\n            \"properties.error\": \"حدث خطأ أثناء حفظ العقار\",\n            // Property Form Fields\n            \"property.title\": \"عنوان العقار\",\n            \"property.title.placeholder\": \"أدخل عنوان العقار\",\n            \"property.description\": \"وصف العقار\",\n            \"property.description.placeholder\": \"أدخل وصف مفصل للعقار\",\n            \"property.price\": \"السعر\",\n            \"property.price.placeholder\": \"أدخل سعر العقار\",\n            \"property.currency\": \"العملة\",\n            \"property.type\": \"نوع العقار\",\n            \"property.type.select\": \"اختر نوع العقار\",\n            \"property.status\": \"حالة العقار\",\n            \"property.status.select\": \"اختر حالة العقار\",\n            \"property.bedrooms\": \"عدد غرف النوم\",\n            \"property.bathrooms\": \"عدد دورات المياه\",\n            \"property.area\": \"المساحة\",\n            \"property.location\": \"الموقع\",\n            \"property.location.placeholder\": \"أدخل موقع العقار\",\n            \"property.address\": \"العنوان\",\n            \"property.address.placeholder\": \"أدخل العنوان التفصيلي\",\n            \"property.city\": \"المدينة\",\n            \"property.city.placeholder\": \"أدخل اسم المدينة\",\n            \"property.country\": \"الدولة\",\n            \"property.images\": \"صور العقار\",\n            \"property.features\": \"المميزات\",\n            \"property.amenities\": \"الخدمات\",\n            \"property.yearBuilt\": \"سنة البناء\",\n            \"property.parking\": \"مواقف السيارات\",\n            \"property.furnished\": \"مفروش\",\n            \"property.petFriendly\": \"يسمح بالحيوانات الأليفة\",\n            // Property Types\n            \"property.type.apartment\": \"شقة\",\n            \"property.type.villa\": \"فيلا\",\n            \"property.type.townhouse\": \"تاون هاوس\",\n            \"property.type.penthouse\": \"بنتهاوس\",\n            \"property.type.studio\": \"استوديو\",\n            \"property.type.office\": \"مكتب\",\n            \"property.type.shop\": \"محل تجاري\",\n            \"property.type.warehouse\": \"مستودع\",\n            \"property.type.land\": \"أرض\",\n            \"property.type.building\": \"مبنى\",\n            // Property Status\n            \"property.status.available\": \"متاح\",\n            \"property.status.sold\": \"مباع\",\n            \"property.status.rented\": \"مؤجر\",\n            \"property.status.pending\": \"قيد المراجعة\",\n            // Countries\n            \"country.uae\": \"الإمارات العربية المتحدة\",\n            \"country.saudi\": \"المملكة العربية السعودية\",\n            \"country.qatar\": \"قطر\",\n            \"country.kuwait\": \"الكويت\",\n            \"country.bahrain\": \"البحرين\",\n            \"country.oman\": \"عمان\",\n            // Validation messages\n            \"validation.required\": \"هذا الحقل مطلوب\",\n            \"validation.email\": \"يرجى إدخال بريد إلكتروني صحيح\",\n            \"validation.minLength\": \"يجب أن يكون الحد الأدنى {{min}} أحرف\",\n            \"validation.maxLength\": \"يجب أن يكون الحد الأقصى {{max}} أحرف\",\n            \"validation.number\": \"يرجى إدخال رقم صحيح\",\n            \"validation.positive\": \"يجب أن يكون الرقم أكبر من الصفر\",\n            // Image upload\n            \"images.upload\": \"رفع الصور\",\n            \"images.drag\": \"اسحب الصور هنا أو انقر للاختيار\",\n            \"images.formats\": \"صور حتى ٨ ميجابايت\",\n            \"images.uploading\": \"جاري رفع الصور...\",\n            \"images.success\": \"تم رفع الصور بنجاح\",\n            \"images.error\": \"خطأ في رفع الصور\",\n            \"images.remove\": \"حذف الصورة\",\n            \"images.preview\": \"معاينة الصورة\"\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/i18n/settings.ts\n"));

/***/ })

});