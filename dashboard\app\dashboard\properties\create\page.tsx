"use client"

import { useState } from 'react'
import { useRouter } from 'next/navigation'
import { PropertyCreateForm } from '@/components/properties/PropertyCreateForm'
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage'
import '@/styles/arabic-properties.css'

export default function CreatePropertyPage() {
  const router = useRouter()
  const { t } = useSimpleLanguage()

  const handleSuccess = () => {
    router.push('/dashboard/properties')
  }

  const handleCancel = () => {
    router.push('/dashboard/properties')
  }

  return (
    <div className="min-h-screen property-form-dark rtl arabic-text">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="property-header-dark">
          <h1 className="arabic-heading">
            {t('properties.create')}
          </h1>
          <p className="text-slate-400">
            {t('properties.subtitle')}
          </p>
        </div>

        {/* Form */}
        <PropertyCreateForm
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </div>
  )
}
