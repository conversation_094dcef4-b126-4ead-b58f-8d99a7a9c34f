"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/properties/page",{

/***/ "(app-pages-browser)/./app/dashboard/properties/page.tsx":
/*!*******************************************!*\
  !*** ./app/dashboard/properties/page.tsx ***!
  \*******************************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ PropertiesPage)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_navigation__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/navigation */ \"(app-pages-browser)/./node_modules/next/dist/api/navigation.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/plus.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/grid-3x3.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/list.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/eye.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/square-pen.js\");\n/* harmony import */ var _barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__ = __webpack_require__(/*! __barrel_optimize__?names=Edit,Eye,Grid,List,Plus,Search,Trash2!=!lucide-react */ \"(app-pages-browser)/./node_modules/lucide-react/dist/esm/icons/trash-2.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/button */ \"(app-pages-browser)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_input__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @/components/ui/input */ \"(app-pages-browser)/./components/ui/input.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! @/components/ui/card */ \"(app-pages-browser)/./components/ui/card.tsx\");\n/* harmony import */ var _components_ui_badge__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! @/components/ui/badge */ \"(app-pages-browser)/./components/ui/badge.tsx\");\n/* harmony import */ var _components_ui_select__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! @/components/ui/select */ \"(app-pages-browser)/./components/ui/select.tsx\");\n/* harmony import */ var _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! @/hooks/useSimpleLanguage */ \"(app-pages-browser)/./hooks/useSimpleLanguage.tsx\");\n/* harmony import */ var _lib_utils_formatPrice__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! @/lib/utils/formatPrice */ \"(app-pages-browser)/./lib/utils/formatPrice.ts\");\n/* __next_internal_client_entry_do_not_use__ default auto */ \nvar _s = $RefreshSig$();\n\n\n\n\n\n\n\n\n\n\nfunction PropertiesPage() {\n    _s();\n    const router = (0,next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter)();\n    const { language } = (0,_hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_8__.useSimpleLanguage)();\n    const [properties, setProperties] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)([]);\n    const [stats, setStats] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    const [viewMode, setViewMode] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('grid');\n    const [searchTerm, setSearchTerm] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('');\n    const [filterType, setFilterType] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('الكل');\n    const [filterStatus, setFilterStatus] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)('الكل');\n    // Removed dialog states - now using dedicated pages\n    // Fetch properties and stats\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)({\n        \"PropertiesPage.useEffect\": ()=>{\n            fetchProperties();\n            fetchStats();\n        }\n    }[\"PropertiesPage.useEffect\"], [\n        searchTerm,\n        filterType,\n        filterStatus\n    ]);\n    const fetchProperties = async ()=>{\n        try {\n            setLoading(true);\n            const params = new URLSearchParams();\n            if (searchTerm) params.append('search', searchTerm);\n            if (filterType && filterType !== 'الكل' && filterType !== 'ALL') params.append('type', filterType);\n            if (filterStatus && filterStatus !== 'الكل' && filterStatus !== 'ALL') params.append('status', filterStatus);\n            params.append('isActive', 'true');\n            const response = await fetch(\"/api/v1/properties?\".concat(params));\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setProperties(data.data.properties);\n                }\n            } else {\n                // Fallback to mock data if backend is not available\n                console.log('Backend not available, using mock data');\n                setProperties([\n                    {\n                        id: '1',\n                        title: 'Luxury Villa in Dubai Marina',\n                        titleAr: 'فيلا فاخرة في دبي مارينا',\n                        description: 'Beautiful 4-bedroom villa with sea view',\n                        descriptionAr: 'فيلا جميلة من 4 غرف نوم مع إطلالة على البحر',\n                        price: 2500000,\n                        currency: 'AED',\n                        type: 'VILLA',\n                        status: 'AVAILABLE',\n                        bedrooms: 4,\n                        bathrooms: 3,\n                        area: 350,\n                        location: 'Dubai Marina',\n                        locationAr: 'دبي مارينا',\n                        address: '123 Marina Walk',\n                        addressAr: '123 ممشى المارينا',\n                        city: 'Dubai',\n                        cityAr: 'دبي',\n                        images: [\n                            '/placeholder.jpg'\n                        ],\n                        features: [\n                            'Swimming Pool',\n                            'Gym',\n                            'Parking'\n                        ],\n                        featuresAr: [\n                            'مسبح',\n                            'صالة رياضية',\n                            'موقف سيارات'\n                        ],\n                        amenities: [\n                            '24/7 Security',\n                            'Concierge'\n                        ],\n                        amenitiesAr: [\n                            'أمن 24/7',\n                            'خدمة الكونسيرج'\n                        ],\n                        isFeatured: true,\n                        isActive: true,\n                        viewCount: 125,\n                        createdAt: new Date().toISOString()\n                    },\n                    {\n                        id: '2',\n                        title: 'Modern Apartment in Downtown',\n                        titleAr: 'شقة حديثة في وسط المدينة',\n                        description: 'Spacious 2-bedroom apartment',\n                        descriptionAr: 'شقة واسعة من غرفتي نوم',\n                        price: 1200000,\n                        currency: 'AED',\n                        type: 'APARTMENT',\n                        status: 'AVAILABLE',\n                        bedrooms: 2,\n                        bathrooms: 2,\n                        area: 120,\n                        location: 'Downtown Dubai',\n                        locationAr: 'وسط مدينة دبي',\n                        address: '456 Sheikh Zayed Road',\n                        addressAr: '456 شارع الشيخ زايد',\n                        city: 'Dubai',\n                        cityAr: 'دبي',\n                        images: [\n                            '/placeholder.jpg'\n                        ],\n                        features: [\n                            'Balcony',\n                            'Built-in Wardrobes'\n                        ],\n                        featuresAr: [\n                            'شرفة',\n                            'خزائن مدمجة'\n                        ],\n                        amenities: [\n                            'Gym',\n                            'Pool'\n                        ],\n                        amenitiesAr: [\n                            'صالة رياضية',\n                            'مسبح'\n                        ],\n                        isFeatured: false,\n                        isActive: true,\n                        viewCount: 89,\n                        createdAt: new Date().toISOString()\n                    }\n                ]);\n            }\n        } catch (error) {\n            console.error('Error fetching properties:', error);\n            // Fallback to empty array on error\n            setProperties([]);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const fetchStats = async ()=>{\n        try {\n            const response = await fetch('/api/v1/properties/stats');\n            if (response.ok) {\n                const data = await response.json();\n                if (data.success) {\n                    setStats(data.data);\n                }\n            } else {\n                // Fallback to mock stats if backend is not available\n                console.log('Backend not available, using mock stats');\n                setStats({\n                    total: 2,\n                    available: 2,\n                    sold: 0,\n                    rented: 0,\n                    featured: 1,\n                    byType: {\n                        APARTMENT: 1,\n                        VILLA: 1,\n                        TOWNHOUSE: 0,\n                        PENTHOUSE: 0,\n                        STUDIO: 0,\n                        OFFICE: 0,\n                        SHOP: 0,\n                        WAREHOUSE: 0,\n                        LAND: 0,\n                        BUILDING: 0\n                    }\n                });\n            }\n        } catch (error) {\n            console.error('Error fetching stats:', error);\n            // Fallback to default stats on error\n            setStats({\n                total: 0,\n                available: 0,\n                sold: 0,\n                rented: 0,\n                featured: 0,\n                byType: {\n                    APARTMENT: 0,\n                    VILLA: 0,\n                    TOWNHOUSE: 0,\n                    PENTHOUSE: 0,\n                    STUDIO: 0,\n                    OFFICE: 0,\n                    SHOP: 0,\n                    WAREHOUSE: 0,\n                    LAND: 0,\n                    BUILDING: 0\n                }\n            });\n        }\n    };\n    const handleDeleteProperty = async (id)=>{\n        if (!confirm(language === 'ar' ? 'هل أنت متأكد من حذف هذا العقار؟' : 'Are you sure you want to delete this property?')) {\n            return;\n        }\n        try {\n            const response = await fetch(\"/api/v1/properties/\".concat(id), {\n                method: 'DELETE'\n            });\n            if (response.ok) {\n                fetchProperties();\n                fetchStats();\n            }\n        } catch (error) {\n            console.error('Error deleting property:', error);\n        }\n    };\n    const formatPriceLocal = (price, currency)=>{\n        return (0,_lib_utils_formatPrice__WEBPACK_IMPORTED_MODULE_9__.formatPrice)(price, currency, language === 'ar' ? 'ar-AE' : 'en-US');\n    };\n    const getStatusColor = (status)=>{\n        switch(status){\n            case 'AVAILABLE':\n                return 'bg-green-600 text-white';\n            case 'SOLD':\n                return 'bg-red-600 text-white';\n            case 'RENTED':\n                return 'bg-blue-600 text-white';\n            case 'PENDING':\n                return 'bg-yellow-600 text-black';\n            case 'RESERVED':\n                return 'bg-orange-600 text-white';\n            default:\n                return 'bg-gray-600 text-white';\n        }\n    };\n    const getStatusText = (status)=>{\n        switch(status){\n            case 'AVAILABLE':\n                return 'متاح';\n            case 'SOLD':\n                return 'مباع';\n            case 'RENTED':\n                return 'مؤجر';\n            case 'PENDING':\n                return 'قيد المراجعة';\n            case 'RESERVED':\n                return 'محجوز';\n            default:\n                return status;\n        }\n    };\n    const getPropertyTitle = (property)=>{\n        return language === 'ar' && property.titleAr ? property.titleAr : property.title;\n    };\n    const getPropertyLocation = (property)=>{\n        return language === 'ar' && property.locationAr ? property.locationAr : property.location;\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"space-y-6\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-3xl font-bold text-gray-900 dark:text-white\",\n                                children: language === 'ar' ? 'العقارات' : 'Properties'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 282,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-gray-600 dark:text-gray-400\",\n                                children: language === 'ar' ? 'إدارة العقارات والممتلكات' : 'Manage properties and real estate'\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 285,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 281,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                        onClick: ()=>router.push('/dashboard/properties/create'),\n                        className: \"bg-blue-600 hover:bg-blue-700 text-white flex items-center gap-2 shadow-lg hover:shadow-xl transition-all duration-200\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                className: \"h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 293,\n                                columnNumber: 11\n                            }, this),\n                            language === 'ar' ? 'إضافة عقار' : 'Add Property'\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 289,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 280,\n                columnNumber: 7\n            }, this),\n            stats && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"grid grid-cols-1 md:grid-cols-2 lg:grid-cols-5 gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'إجمالي العقارات' : 'Total Properties'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 303,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 302,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold\",\n                                    children: stats.total\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 308,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 307,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 301,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'متاح' : 'Available'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 313,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 312,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-green-600\",\n                                    children: stats.available\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 318,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 317,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 311,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'مباع' : 'Sold'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 323,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 322,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-red-600\",\n                                    children: stats.sold\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 328,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 327,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 321,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'مؤجر' : 'Rented'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 333,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 332,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-blue-600\",\n                                    children: stats.rented\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 338,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 337,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 331,\n                        columnNumber: 11\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardHeader, {\n                                className: \"pb-2\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardTitle, {\n                                    className: \"text-sm font-medium text-gray-600 dark:text-gray-400\",\n                                    children: language === 'ar' ? 'مميز' : 'Featured'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 343,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 342,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"text-2xl font-bold text-purple-600\",\n                                    children: stats.featured\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 348,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 347,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 341,\n                        columnNumber: 11\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 300,\n                columnNumber: 9\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex flex-col sm:flex-row gap-4\",\n                children: [\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"relative flex-1\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_11__[\"default\"], {\n                                className: \"absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 357,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_input__WEBPACK_IMPORTED_MODULE_4__.Input, {\n                                placeholder: language === 'ar' ? 'البحث في العقارات...' : 'Search properties...',\n                                value: searchTerm,\n                                onChange: (e)=>setSearchTerm(e.target.value),\n                                className: \"pl-10\"\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 358,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 356,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                        value: filterType,\n                        onValueChange: setFilterType,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                className: \"w-full sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                    placeholder: language === 'ar' ? 'نوع العقار' : 'Property Type'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 367,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 366,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"الكل\",\n                                        children: \"جميع الأنواع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 370,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"APARTMENT\",\n                                        children: \"شقة\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 371,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"VILLA\",\n                                        children: \"فيلا\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 372,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"TOWNHOUSE\",\n                                        children: \"تاون هاوس\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 373,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"OFFICE\",\n                                        children: \"مكتب\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 374,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 369,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 365,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.Select, {\n                        value: filterStatus,\n                        onValueChange: setFilterStatus,\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectTrigger, {\n                                className: \"w-full sm:w-48\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectValue, {\n                                    placeholder: language === 'ar' ? 'الحالة' : 'Status'\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 379,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 378,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectContent, {\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"الكل\",\n                                        children: \"جميع الحالات\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 382,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"AVAILABLE\",\n                                        children: \"متاح\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 383,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"SOLD\",\n                                        children: \"مباع\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 384,\n                                        columnNumber: 13\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_select__WEBPACK_IMPORTED_MODULE_7__.SelectItem, {\n                                        value: \"RENTED\",\n                                        children: \"مؤجر\"\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 385,\n                                        columnNumber: 13\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 381,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 377,\n                        columnNumber: 9\n                    }, this),\n                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex gap-2\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: viewMode === 'grid' ? 'default' : 'outline',\n                                size: \"sm\",\n                                onClick: ()=>setViewMode('grid'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_12__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 394,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 389,\n                                columnNumber: 11\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                variant: viewMode === 'list' ? 'default' : 'outline',\n                                size: \"sm\",\n                                onClick: ()=>setViewMode('list'),\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_13__[\"default\"], {\n                                    className: \"h-4 w-4\"\n                                }, void 0, false, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 401,\n                                    columnNumber: 13\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 396,\n                                columnNumber: 11\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 388,\n                        columnNumber: 9\n                    }, this)\n                ]\n            }, void 0, true, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 355,\n                columnNumber: 7\n            }, this),\n            loading ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex justify-center items-center h-64\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-8 w-8 border-b-2 border-gray-900 dark:border-white\"\n                }, void 0, false, {\n                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                    lineNumber: 409,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 408,\n                columnNumber: 9\n            }, this) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: viewMode === 'grid' ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6' : 'space-y-4',\n                children: properties.map((property)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.Card, {\n                        className: \"overflow-hidden hover:shadow-lg transition-shadow\",\n                        children: viewMode === 'grid' ? /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.Fragment, {\n                            children: [\n                                property.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                    className: \"relative h-48 bg-gray-200 dark:bg-gray-700\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: property.images[0],\n                                            alt: getPropertyTitle(property),\n                                            className: \"w-full h-full object-cover\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 419,\n                                            columnNumber: 23\n                                        }, this),\n                                        property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                            className: \"absolute top-2 left-2 bg-purple-600\",\n                                            children: language === 'ar' ? 'مميز' : 'Featured'\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 425,\n                                            columnNumber: 25\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 418,\n                                    columnNumber: 21\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                                    className: \"p-4\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-start mb-2\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                    className: \"font-semibold text-lg truncate\",\n                                                    children: getPropertyTitle(property)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 433,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                    className: getStatusColor(property.status),\n                                                    children: getStatusText(property.status)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 434,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 432,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                            className: \"text-gray-600 dark:text-gray-400 text-sm mb-2\",\n                                            children: getPropertyLocation(property)\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 438,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center mb-3\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xl font-bold text-blue-600\",\n                                                    children: formatPriceLocal(property.price, property.currency)\n                                                }, void 0, false, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 442,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-2 text-sm text-gray-500\",\n                                                    children: [\n                                                        property.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                property.bedrooms,\n                                                                \" \",\n                                                                language === 'ar' ? 'غرف' : 'bed'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 446,\n                                                            columnNumber: 47\n                                                        }, this),\n                                                        property.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                property.bathrooms,\n                                                                \" \",\n                                                                language === 'ar' ? 'حمام' : 'bath'\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 447,\n                                                            columnNumber: 48\n                                                        }, this),\n                                                        property.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                            children: [\n                                                                property.area,\n                                                                \"m\\xb2\"\n                                                            ]\n                                                        }, void 0, true, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 448,\n                                                            columnNumber: 43\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 445,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 441,\n                                            columnNumber: 21\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                            className: \"flex justify-between items-center\",\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"flex gap-1\",\n                                                    children: [\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>router.push(\"/dashboard/properties/\".concat(property.id)),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 454,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 453,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>router.push(\"/dashboard/properties/\".concat(property.id, \"/edit\")),\n                                                            className: \"hover:bg-blue-50 hover:border-blue-300 transition-colors\",\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 462,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 456,\n                                                            columnNumber: 25\n                                                        }, this),\n                                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                            size: \"sm\",\n                                                            variant: \"outline\",\n                                                            onClick: ()=>handleDeleteProperty(property.id),\n                                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                className: \"h-4 w-4\"\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 465,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        }, void 0, false, {\n                                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                            lineNumber: 464,\n                                                            columnNumber: 25\n                                                        }, this)\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 452,\n                                                    columnNumber: 23\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                    className: \"text-xs text-gray-500\",\n                                                    children: [\n                                                        language === 'ar' ? 'المشاهدات:' : 'Views:',\n                                                        \" \",\n                                                        property.viewCount\n                                                    ]\n                                                }, void 0, true, {\n                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                    lineNumber: 468,\n                                                    columnNumber: 23\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 451,\n                                            columnNumber: 21\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                    lineNumber: 431,\n                                    columnNumber: 19\n                                }, this)\n                            ]\n                        }, void 0, true) : /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_5__.CardContent, {\n                            className: \"p-4\",\n                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex gap-4\",\n                                children: [\n                                    property.images.length > 0 && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"relative w-24 h-24 bg-gray-200 dark:bg-gray-700 rounded\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"img\", {\n                                            src: property.images[0],\n                                            alt: getPropertyTitle(property),\n                                            className: \"w-full h-full object-cover rounded\"\n                                        }, void 0, false, {\n                                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                            lineNumber: 479,\n                                            columnNumber: 25\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 478,\n                                        columnNumber: 23\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"flex-1\",\n                                        children: [\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-start mb-2\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n                                                        className: \"font-semibold text-lg\",\n                                                        children: getPropertyTitle(property)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 488,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-2\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: getStatusColor(property.status),\n                                                                children: getStatusText(property.status)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 490,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            property.isFeatured && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_badge__WEBPACK_IMPORTED_MODULE_6__.Badge, {\n                                                                className: \"bg-purple-600\",\n                                                                children: language === 'ar' ? 'مميز' : 'Featured'\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 494,\n                                                                columnNumber: 29\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 489,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 487,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                                className: \"text-gray-600 dark:text-gray-400 text-sm mb-2\",\n                                                children: getPropertyLocation(property)\n                                            }, void 0, false, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 500,\n                                                columnNumber: 23\n                                            }, this),\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                className: \"flex justify-between items-center\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                        className: \"text-xl font-bold text-blue-600\",\n                                                        children: formatPriceLocal(property.price, property.currency)\n                                                    }, void 0, false, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 504,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-4 text-sm text-gray-500\",\n                                                        children: [\n                                                            property.bedrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.bedrooms,\n                                                                    \" \",\n                                                                    language === 'ar' ? 'غرف' : 'bed'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 508,\n                                                                columnNumber: 49\n                                                            }, this),\n                                                            property.bathrooms && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.bathrooms,\n                                                                    \" \",\n                                                                    language === 'ar' ? 'حمام' : 'bath'\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 509,\n                                                                columnNumber: 50\n                                                            }, this),\n                                                            property.area && /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    property.area,\n                                                                    \"m\\xb2\"\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 510,\n                                                                columnNumber: 45\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                                                children: [\n                                                                    language === 'ar' ? 'المشاهدات:' : 'Views:',\n                                                                    \" \",\n                                                                    property.viewCount\n                                                                ]\n                                                            }, void 0, true, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 511,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 507,\n                                                        columnNumber: 25\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                        className: \"flex gap-1\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>router.push(\"/dashboard/properties/\".concat(property.id)),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_14__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                    lineNumber: 515,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 514,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>router.push(\"/dashboard/properties/\".concat(property.id, \"/edit\")),\n                                                                className: \"hover:bg-blue-50 hover:border-blue-300 transition-colors\",\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_15__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                    lineNumber: 523,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 517,\n                                                                columnNumber: 27\n                                                            }, this),\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_3__.Button, {\n                                                                size: \"sm\",\n                                                                variant: \"outline\",\n                                                                onClick: ()=>handleDeleteProperty(property.id),\n                                                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_Edit_Eye_Grid_List_Plus_Search_Trash2_lucide_react__WEBPACK_IMPORTED_MODULE_16__[\"default\"], {\n                                                                    className: \"h-4 w-4\"\n                                                                }, void 0, false, {\n                                                                    fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                    lineNumber: 526,\n                                                                    columnNumber: 29\n                                                                }, this)\n                                                            }, void 0, false, {\n                                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                                lineNumber: 525,\n                                                                columnNumber: 27\n                                                            }, this)\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                        lineNumber: 513,\n                                                        columnNumber: 25\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                                lineNumber: 503,\n                                                columnNumber: 23\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                        lineNumber: 486,\n                                        columnNumber: 21\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                                lineNumber: 476,\n                                columnNumber: 19\n                            }, this)\n                        }, void 0, false, {\n                            fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                            lineNumber: 475,\n                            columnNumber: 17\n                        }, this)\n                    }, property.id, false, {\n                        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                        lineNumber: 414,\n                        columnNumber: 13\n                    }, this))\n            }, void 0, false, {\n                fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n                lineNumber: 412,\n                columnNumber: 9\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"C:\\\\Users\\\\<USER>\\\\Desktop\\\\code\\\\boot\\\\dashboard\\\\app\\\\dashboard\\\\properties\\\\page.tsx\",\n        lineNumber: 278,\n        columnNumber: 5\n    }, this);\n}\n_s(PropertiesPage, \"fSK+ki2zuOH8XoJ/w9r3SeZEO7o=\", false, function() {\n    return [\n        next_navigation__WEBPACK_IMPORTED_MODULE_2__.useRouter,\n        _hooks_useSimpleLanguage__WEBPACK_IMPORTED_MODULE_8__.useSimpleLanguage\n    ];\n});\n_c = PropertiesPage;\nvar _c;\n$RefreshReg$(_c, \"PropertiesPage\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/dashboard/properties/page.tsx\n"));

/***/ })

});