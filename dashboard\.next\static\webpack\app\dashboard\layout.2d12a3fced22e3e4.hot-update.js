"use strict";
/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
self["webpackHotUpdate_N_E"]("app/dashboard/layout",{

/***/ "(app-pages-browser)/./lib/i18n/settings.ts":
/*!******************************!*\
  !*** ./lib/i18n/settings.ts ***!
  \******************************/
/***/ ((module, __webpack_exports__, __webpack_require__) => {

eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   defaultLanguage: () => (/* binding */ defaultLanguage),\n/* harmony export */   languages: () => (/* binding */ languages),\n/* harmony export */   resources: () => (/* binding */ resources)\n/* harmony export */ });\nconst languages = [\n    \"ar\"\n];\nconst defaultLanguage = \"ar\" // Arabic only\n;\nconst resources = {\n    ar: {\n        translation: {\n            // Sidebar\n            \"sidebar.analytics\": \"التحليلات\",\n            \"sidebar.user\": \"لوحة التحكم\",\n            \"sidebar.clients\": \"العملاء\",\n            \"sidebar.messaging\": \"المراسلة\",\n            \"sidebar.marketing\": \"التسويق\",\n            \"sidebar.campaigns\": \"الحملات\",\n            \"sidebar.templates\": \"القوالب\",\n            \"sidebar.appointments\": \"المواعيد\",\n            \"sidebar.ai-chatbot\": \"الذكاء الاصطناعي\",\n            \"sidebar.users\": \"المستخدمين\",\n            \"sidebar.properties\": \"العقارات\",\n            \"sidebar.settings\": \"الإعدادات\",\n            \"sidebar.profile\": \"الملف الشخصي\",\n            // Properties\n            \"properties.title\": \"العقارات\",\n            \"properties.subtitle\": \"إدارة وإضافة العقارات الجديدة\",\n            \"properties.add\": \"إضافة عقار\",\n            \"properties.create\": \"إنشاء عقار جديد\",\n            \"properties.edit\": \"تعديل العقار\",\n            \"properties.delete\": \"حذف العقار\",\n            \"properties.view\": \"عرض العقار\",\n            \"properties.save\": \"حفظ العقار\",\n            \"properties.cancel\": \"إلغاء\",\n            \"properties.loading\": \"جاري التحميل...\",\n            \"properties.success\": \"تم حفظ العقار بنجاح\",\n            \"properties.error\": \"حدث خطأ أثناء حفظ العقار\",\n            // Property Form Fields\n            \"property.title\": \"عنوان العقار\",\n            \"property.title.placeholder\": \"أدخل عنوان العقار\",\n            \"property.description\": \"وصف العقار\",\n            \"property.description.placeholder\": \"أدخل وصف مفصل للعقار\",\n            \"property.price\": \"السعر\",\n            \"property.price.placeholder\": \"أدخل سعر العقار\",\n            \"property.currency\": \"العملة\",\n            \"property.type\": \"نوع العقار\",\n            \"property.type.select\": \"اختر نوع العقار\",\n            \"property.status\": \"حالة العقار\",\n            \"property.status.select\": \"اختر حالة العقار\",\n            \"property.bedrooms\": \"عدد غرف النوم\",\n            \"property.bathrooms\": \"عدد دورات المياه\",\n            \"property.area\": \"المساحة\",\n            \"property.location\": \"الموقع\",\n            \"property.location.placeholder\": \"أدخل موقع العقار\",\n            \"property.address\": \"العنوان\",\n            \"property.address.placeholder\": \"أدخل العنوان التفصيلي\",\n            \"property.city\": \"المدينة\",\n            \"property.city.placeholder\": \"أدخل اسم المدينة\",\n            \"property.country\": \"الدولة\",\n            \"property.images\": \"صور العقار\",\n            \"property.features\": \"المميزات\",\n            \"property.amenities\": \"الخدمات\",\n            \"property.yearBuilt\": \"سنة البناء\",\n            \"property.parking\": \"مواقف السيارات\",\n            \"property.furnished\": \"مفروش\",\n            \"property.petFriendly\": \"يسمح بالحيوانات الأليفة\",\n            // Property Types\n            \"property.type.apartment\": \"شقة\",\n            \"property.type.villa\": \"فيلا\",\n            \"property.type.townhouse\": \"تاون هاوس\",\n            \"property.type.penthouse\": \"بنتهاوس\",\n            \"property.type.studio\": \"استوديو\",\n            \"property.type.office\": \"مكتب\",\n            \"property.type.shop\": \"محل تجاري\",\n            \"property.type.warehouse\": \"مستودع\",\n            \"property.type.land\": \"أرض\",\n            \"property.type.building\": \"مبنى\",\n            // Property Status\n            \"property.status.available\": \"متاح\",\n            \"property.status.sold\": \"مباع\",\n            \"property.status.rented\": \"مؤجر\",\n            \"property.status.pending\": \"قيد المراجعة\",\n            // Countries\n            \"country.uae\": \"الإمارات العربية المتحدة\",\n            \"country.saudi\": \"المملكة العربية السعودية\",\n            \"country.qatar\": \"قطر\",\n            \"country.kuwait\": \"الكويت\",\n            \"country.bahrain\": \"البحرين\",\n            \"country.oman\": \"عمان\",\n            // Validation messages\n            \"validation.required\": \"هذا الحقل مطلوب\",\n            \"validation.email\": \"يرجى إدخال بريد إلكتروني صحيح\",\n            \"validation.minLength\": \"يجب أن يكون الحد الأدنى {{min}} أحرف\",\n            \"validation.maxLength\": \"يجب أن يكون الحد الأقصى {{max}} أحرف\",\n            \"validation.number\": \"يرجى إدخال رقم صحيح\",\n            \"validation.positive\": \"يجب أن يكون الرقم أكبر من الصفر\",\n            // Image upload\n            \"images.upload\": \"رفع الصور\",\n            \"images.drag\": \"اسحب الصور هنا أو انقر للاختيار\",\n            \"images.formats\": \"صور حتى ٨ ميجابايت\",\n            \"images.uploading\": \"جاري رفع الصور...\",\n            \"images.success\": \"تم رفع الصور بنجاح\",\n            \"images.error\": \"خطأ في رفع الصور\",\n            \"images.remove\": \"حذف الصورة\",\n            \"images.preview\": \"معاينة الصورة\",\n            \"images.fileType\": \"يرجى اختيار ملفات صور فقط\",\n            \"images.fileSize\": \"حجم الصورة يجب أن يكون أقل من ٨ ميجابايت\"\n        }\n    }\n};\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/i18n/settings.ts\n"));

/***/ })

});