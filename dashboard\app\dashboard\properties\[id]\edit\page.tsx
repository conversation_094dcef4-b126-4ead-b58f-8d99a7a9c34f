"use client"

import { useState, useEffect } from 'react'
import { usePara<PERSON>, useRouter } from 'next/navigation'
import { useSimpleLanguage } from '@/hooks/useSimpleLanguage'
import { propertyService } from '@/services/propertyService'
import { PropertyCreateForm } from '@/components/properties/PropertyCreateForm'
import { Button } from '@/components/ui/button'
import { ArrowRight } from 'lucide-react'
import { useToast } from '@/hooks/use-toast'
import '@/styles/arabic-properties.css'

interface Property {
  id: string
  title: string
  titleAr?: string
  description: string
  descriptionAr?: string
  price: number
  currency: string
  type: string
  status: string
  bedrooms?: number
  bathrooms?: number
  area?: number
  location: string
  locationAr?: string
  address: string
  addressAr?: string
  city: string
  cityAr?: string
  country: string
  countryAr?: string
  latitude?: number
  longitude?: number
  images: string[]
  features: string[]
  featuresAr: string[]
  amenities: string[]
  amenitiesAr: string[]
  yearBuilt?: number
  parking?: number
  furnished: boolean
  petFriendly: boolean
  utilities?: string
  utilitiesAr?: string
  contactInfo?: string
  agentId?: string
  isActive: boolean
  isFeatured: boolean
  viewCount: number
  createdAt: string
  updatedAt: string
  agent?: {
    id: string
    name: string
    email: string
  }
}

export default function PropertyEditPage() {
  const params = useParams()
  const router = useRouter()
  const { t } = useSimpleLanguage()
  const { toast } = useToast()
  
  const [property, setProperty] = useState<Property | null>(null)
  const [isLoading, setIsLoading] = useState(true)

  const propertyId = params.id as string

  useEffect(() => {
    if (propertyId) {
      fetchProperty()
    }
  }, [propertyId])

  const fetchProperty = async () => {
    try {
      setIsLoading(true)
      const data = await propertyService.getPropertyById(propertyId)
      setProperty(data)
    } catch (error) {
      console.error('Error fetching property:', error)
      toast({
        title: 'خطأ في تحميل العقار',
        description: 'حدث خطأ أثناء تحميل بيانات العقار',
        variant: 'destructive',
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleSuccess = () => {
    toast({
      title: 'تم تحديث العقار',
      description: 'تم تحديث العقار بنجاح',
    })
    router.push(`/dashboard/properties/${propertyId}`)
  }

  const handleCancel = () => {
    router.push(`/dashboard/properties/${propertyId}`)
  }

  const handleBack = () => {
    router.push(`/dashboard/properties/${propertyId}`)
  }

  if (isLoading) {
    return (
      <div className="min-h-screen property-form-dark rtl arabic-text">
        <div className="max-w-4xl mx-auto p-6">
          <div className="flex items-center justify-center min-h-[400px]">
            <div className="loading-spinner" />
            <span className="mr-3 text-lg">جاري تحميل العقار...</span>
          </div>
        </div>
      </div>
    )
  }

  if (!property) {
    return (
      <div className="min-h-screen property-form-dark rtl arabic-text">
        <div className="max-w-4xl mx-auto p-6">
          <div className="text-center py-12">
            <h1 className="text-2xl font-bold text-red-400 mb-4">العقار غير موجود</h1>
            <p className="text-gray-400 mb-6">لم يتم العثور على العقار المطلوب</p>
            <Button onClick={handleBack} className="btn-primary">
              <ArrowRight className="h-4 w-4 ml-2" />
              العودة إلى قائمة العقارات
            </Button>
          </div>
        </div>
      </div>
    )
  }

  // Convert property data to form format
  const initialData = {
    title: property.title,
    description: property.description,
    price: property.price,
    currency: property.currency,
    type: property.type,
    status: property.status,
    bedrooms: property.bedrooms || 0,
    bathrooms: property.bathrooms || 0,
    area: property.area || 0,
    location: property.location,
    address: property.address,
    city: property.city,
    country: property.country,
    images: property.images,
    features: property.features,
    amenities: property.amenities,
    yearBuilt: property.yearBuilt,
    parking: property.parking,
    furnished: property.furnished,
    petFriendly: property.petFriendly,
    utilities: property.utilities,
    contactInfo: property.contactInfo,
    agentId: property.agentId,
    isActive: property.isActive,
    isFeatured: property.isFeatured,
  }

  return (
    <div className="min-h-screen property-form-dark rtl arabic-text">
      <div className="max-w-4xl mx-auto p-6">
        {/* Header */}
        <div className="property-header-dark mb-6">
          <Button
            onClick={handleBack}
            variant="ghost"
            className="text-gray-400 hover:text-white mb-4"
          >
            <ArrowRight className="h-4 w-4 ml-2" />
            العودة إلى العقار
          </Button>
          <h1 className="arabic-heading text-3xl font-bold text-white">
            تعديل العقار
          </h1>
          <p className="text-gray-400 mt-2">
            تحديث معلومات العقار: {property.title}
          </p>
        </div>

        {/* Edit Form */}
        <PropertyCreateForm
          initialData={initialData}
          isEdit={true}
          propertyId={propertyId}
          onSuccess={handleSuccess}
          onCancel={handleCancel}
        />
      </div>
    </div>
  )
}
